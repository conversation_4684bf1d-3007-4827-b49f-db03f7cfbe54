import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';

// Generate a strong random password (used only when no configured default is provided)
function generateRandomPassword(length = 24): string {
  // crypto.randomBytes is secure; base64 then replace non-url-safe chars to keep it readable
  return crypto.randomBytes(Math.ceil(length * 3 / 4)).toString('base64').replace(/\+/g, 'A').replace(/\//g, 'B').slice(0, length);
}

// Helper: log a one-time hint for generated admin password if present in memory
function logGeneratedPasswordIfAny(pw: string | undefined) {
  if (!pw) return;
  // Only log a short hint, avoid printing secrets in CI logs; for local dev this helps
  console.log(`🔐 Default admin password set (length ${pw.length}). If this is a generated password, consider setting a static one in your security config for reproducible environments.`);
}

// Database configuration
const DB_PATH = process.env.DB_PATH || path.join(process.cwd(), 'data', 'rpa.db');
const DB_DIR = path.dirname(DB_PATH);

// Ensure database directory exists
if (!fs.existsSync(DB_DIR)) {
  fs.mkdirSync(DB_DIR, { recursive: true });
}

// Create database connection
const database = new Database(DB_PATH);

// Export database with explicit type
export const db: Database.Database = database;

// Enable WAL mode for better performance
database.pragma('journal_mode = WAL');
database.pragma('synchronous = NORMAL');
database.pragma('cache_size = 1000');
database.pragma('temp_store = memory');

// Database version management
const CURRENT_DB_VERSION = 5;

function getDatabaseVersion(): number {
  try {
    const result = database.prepare('PRAGMA user_version').get() as { user_version: number };
    return result.user_version;
  } catch (error) {
    return 0;
  }
}

function setDatabaseVersion(version: number): void {
  database.prepare(`PRAGMA user_version = ${version}`).run();
}

// Database migrations
function runMigrations() {
  const currentVersion = getDatabaseVersion();
  console.log(`📊 Current database version: ${currentVersion}`);

  if (currentVersion < 2) {
    console.log('🔄 Running migration to version 2: Updating executions table for large base64 data...');

    // Check if executions table exists before migrating
    const executionsTableExists = database.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='executions'
    `).get();

    if (executionsTableExists) {
      // SQLite doesn't support ALTER COLUMN directly, so we need to recreate the table
      database.exec(`
        -- Create new executions table with BLOB for results
        CREATE TABLE IF NOT EXISTS executions_new (
          id TEXT PRIMARY KEY,
          flow_id TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'pending',
          started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          completed_at DATETIME NULL,
          results BLOB DEFAULT '{}', -- Changed to BLOB for large data
          error TEXT NULL,
          FOREIGN KEY (flow_id) REFERENCES flows (id) ON DELETE CASCADE
        );

        -- Copy existing data
        INSERT INTO executions_new (id, flow_id, status, started_at, completed_at, results, error)
        SELECT id, flow_id, status, started_at, completed_at, results, error
        FROM executions;

        -- Drop old table and rename new one
        DROP TABLE executions;
        ALTER TABLE executions_new RENAME TO executions;
      `);
    }

    // Check if execution_logs table exists before migrating
    const executionLogsTableExists = database.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='execution_logs'
    `).get();

    if (executionLogsTableExists) {
      // Also update execution_logs table for large data
      database.exec(`
        -- Create new execution_logs table with BLOB for data
        CREATE TABLE IF NOT EXISTS execution_logs_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          execution_id TEXT NOT NULL,
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
          level TEXT NOT NULL,
          message TEXT NOT NULL,
          step_id TEXT NULL,
          data BLOB NULL, -- Changed to BLOB for large data
          FOREIGN KEY (execution_id) REFERENCES executions (id) ON DELETE CASCADE
        );

        -- Copy existing data
        INSERT INTO execution_logs_new (id, execution_id, timestamp, level, message, step_id, data)
        SELECT id, execution_id, timestamp, level, message, step_id, data
        FROM execution_logs;

        -- Drop old table and rename new one
        DROP TABLE execution_logs;
        ALTER TABLE execution_logs_new RENAME TO execution_logs;
      `);
    }

    console.log('✅ Migration to version 2 completed');
  }

  if (currentVersion < 3) {
    console.log('🔄 Running migration to version 3: Adding customer_id to executions table...');

    // Check if executions table exists and needs customer_id column
    const executionsTableExists = database.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='executions'
    `).get();

    if (executionsTableExists) {
      // Check if customer_id column already exists
      const customerIdColumnExists = database.prepare(`
        PRAGMA table_info(executions)
      `).all().some((col: any) => col.name === 'customer_id');

      if (!customerIdColumnExists) {
        // Add customer_id column to executions table
        database.exec(`
          -- Create new executions table with customer_id
          CREATE TABLE IF NOT EXISTS executions_new (
            id TEXT PRIMARY KEY,
            flow_id TEXT NOT NULL,
            customer_id TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'pending',
            started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME NULL,
            results BLOB DEFAULT '{}',
            error TEXT NULL,
            FOREIGN KEY (flow_id) REFERENCES flows (id) ON DELETE CASCADE,
            FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
          );

          -- Copy existing data and populate customer_id from flows table
          INSERT INTO executions_new (id, flow_id, customer_id, status, started_at, completed_at, results, error)
          SELECT e.id, e.flow_id, f.customer_id, e.status, e.started_at, e.completed_at, e.results, e.error
          FROM executions e
          JOIN flows f ON e.flow_id = f.id;

          -- Drop old table and rename new one
          DROP TABLE executions;
          ALTER TABLE executions_new RENAME TO executions;
        `);
      }
    }

    console.log('✅ Migration to version 3 completed');
  }

  if (currentVersion < 4) {
    console.log('🔄 Running migration to version 4: Create token_usage_logs table...');

    database.exec(`
      CREATE TABLE IF NOT EXISTS token_usage_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        operation TEXT NOT NULL,
        model TEXT NOT NULL,
        provider TEXT NOT NULL,
        prompt_tokens INTEGER NOT NULL,
        completion_tokens INTEGER NOT NULL,
        total_tokens INTEGER NOT NULL,
        step_id TEXT NULL,
        user_id TEXT NULL,
        metadata TEXT NULL
      );

      CREATE INDEX IF NOT EXISTS idx_token_usage_logs_timestamp ON token_usage_logs (timestamp);
      CREATE INDEX IF NOT EXISTS idx_token_usage_logs_operation ON token_usage_logs (operation);
      CREATE INDEX IF NOT EXISTS idx_token_usage_logs_model ON token_usage_logs (model);
      CREATE INDEX IF NOT EXISTS idx_token_usage_logs_provider ON token_usage_logs (provider);
    `);

    console.log('✅ Migration to version 4 completed');
  }

  if (currentVersion < 5) {
    console.log('🔄 Running migration to version 5: Create users table and seed admin...');
    database.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT NOT NULL UNIQUE,
        email TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('admin','user')),
        is_active BOOLEAN NOT NULL DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login_at DATETIME NULL
      );
    `);

    // Seed default admin if no users exist
    const userCountRow = database.prepare('SELECT COUNT(*) as count FROM users').get() as any;
    if (userCountRow.count === 0) {
      const bcrypt = require('bcrypt');
      const { securityConfig } = require('../config/security');
      const { generateId } = require('@rpa-project/shared');
      // Use configured default admin password if provided, otherwise generate a strong random one
      const defaultPassword = securityConfig?.defaultAdminPassword || generateRandomPassword();
      const passwordHash = bcrypt.hashSync(defaultPassword, securityConfig.security.bcryptRounds);
      const id = generateId();
      const now = new Date().toISOString();
      database.prepare(`
        INSERT INTO users (id, username, email, password_hash, role, is_active, created_at, updated_at)
        VALUES (?, 'admin', '<EMAIL>', ?, 'admin', 1, ?, ?)
      `).run(id, passwordHash, now, now);
  console.log('👤 Seeded default admin user: admin');
  // Note: If a random password was used the value was only available at startup in the logs.
  // Consider setting securityConfig.defaultAdminPassword in config for reproducible credentials.
    }

    console.log('✅ Migration to version 5 completed');
  }


  // Set the current version
  if (currentVersion < CURRENT_DB_VERSION) {
    setDatabaseVersion(CURRENT_DB_VERSION);
    console.log(`✅ Database updated to version ${CURRENT_DB_VERSION}`);
  }

  // Ensure customer_id index exists after all migrations
  try {
    database.exec(`CREATE INDEX IF NOT EXISTS idx_executions_customer_id ON executions (customer_id);`);
  } catch (error) {
    // Index creation might fail if column doesn't exist yet, which is fine
    console.log('Note: customer_id index will be created after migration completes');
  }
}

// Initialize database schema
function initializeDatabase() {
  console.log(`📁 Initializing SQLite database at: ${DB_PATH}`);

  // Run migrations first
  runMigrations();

  // Create flows table
  database.exec(`
    CREATE TABLE IF NOT EXISTS flows (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT DEFAULT '',
      customer_id TEXT NOT NULL,
      steps TEXT NOT NULL, -- JSON string
      variables TEXT DEFAULT '{}', -- JSON string
      settings TEXT DEFAULT '{}', -- JSON string
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
    )
  `);

  // Create executions table
  database.exec(`
    CREATE TABLE IF NOT EXISTS executions (
      id TEXT PRIMARY KEY,
      flow_id TEXT NOT NULL,
      customer_id TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'pending',
      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      completed_at DATETIME NULL,
      results BLOB DEFAULT '{}', -- BLOB for large data (base64 strings)
      error TEXT NULL,
      FOREIGN KEY (flow_id) REFERENCES flows (id) ON DELETE CASCADE,
      FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
    )
  `);

  // Create users table
  database.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT NOT NULL UNIQUE,
      email TEXT NOT NULL UNIQUE,
      password_hash TEXT NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('admin','user')),
      is_active BOOLEAN NOT NULL DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login_at DATETIME NULL
    )
  `);

  // Create execution_logs table
  database.exec(`
    CREATE TABLE IF NOT EXISTS execution_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      execution_id TEXT NOT NULL,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      level TEXT NOT NULL,
      message TEXT NOT NULL,
      step_id TEXT NULL,
      data BLOB NULL, -- BLOB for large data (base64 strings)
      FOREIGN KEY (execution_id) REFERENCES executions (id) ON DELETE CASCADE
    )
  `);

  // Create schedules table
  database.exec(`
    CREATE TABLE IF NOT EXISTS schedules (
      id TEXT PRIMARY KEY,
      flow_id TEXT NOT NULL,
      name TEXT NOT NULL,
      description TEXT DEFAULT '',
      cron_expression TEXT NOT NULL,
      timezone TEXT DEFAULT 'UTC',
      enabled BOOLEAN DEFAULT 1,
      variables TEXT DEFAULT '{}', -- JSON string
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_run_at DATETIME NULL,
      next_run_at DATETIME NULL,
      FOREIGN KEY (flow_id) REFERENCES flows (id) ON DELETE CASCADE
    )
  `);

  // Create credentials table
  database.exec(`
    CREATE TABLE IF NOT EXISTS credentials (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT DEFAULT '',
      type TEXT NOT NULL CHECK (type IN ('password', '2fa')),
      username TEXT NULL, -- Only for password type
      encrypted_password TEXT NULL, -- Only for password type, encrypted
      encrypted_secret TEXT NULL, -- Only for 2fa type, encrypted
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create customers table
  database.exec(`
    CREATE TABLE IF NOT EXISTS customers (
      id TEXT PRIMARY KEY,
      customer_number TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL CHECK (length(name) <= 200),
      visma_number TEXT DEFAULT '',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create trigger to update updated_at on users
  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_users_updated_at
    AFTER UPDATE ON users
    FOR EACH ROW
    BEGIN
      UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // Create customer_tokens table
  database.exec(`
    CREATE TABLE IF NOT EXISTS customer_tokens (
      id TEXT PRIMARY KEY,
      customer_id TEXT NOT NULL,
      name TEXT NOT NULL CHECK (length(name) <= 100),
      description TEXT DEFAULT '',
      provider TEXT NOT NULL DEFAULT 'manual' CHECK (provider IN ('eEkonomi', 'Fortnox', 'manual')),
      encrypted_api_token TEXT NULL, -- Encrypted API token
      encrypted_refresh_token TEXT NULL, -- Encrypted refresh token
      expires_at DATETIME NULL, -- Token expiration time
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
      UNIQUE(customer_id, name) -- Ensure unique token names per customer
    )
  `);

  // Migration: Add new columns to existing customer_tokens table if they don't exist
  try {
    database.exec(`ALTER TABLE customer_tokens ADD COLUMN provider TEXT NOT NULL DEFAULT 'manual' CHECK (provider IN ('eEkonomi', 'Fortnox', 'manual'))`);
  } catch (error: any) {
    // Column already exists, ignore error
    if (!error.message.includes('duplicate column name')) {
      console.warn('Migration warning for provider column:', error.message);
    }
  }

  try {
    database.exec(`ALTER TABLE customer_tokens ADD COLUMN expires_at DATETIME NULL`);

  // Safety: ensure admin user exists if users table is empty
  try {
    const userTable = database.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='users'").get();
    if (userTable) {
      const row = database.prepare('SELECT COUNT(*) as count FROM users').get() as any;
      if (row && row.count === 0) {
  const bcrypt = require('bcrypt');
  const { securityConfig } = require('../config/security');
  const { generateId } = require('@rpa-project/shared');
  const defaultPassword = securityConfig?.defaultAdminPassword || generateRandomPassword();
  const passwordHash = bcrypt.hashSync(defaultPassword, securityConfig.security.bcryptRounds);
        const id = generateId();
        const now = new Date().toISOString();
        database.prepare(`
          INSERT INTO users (id, username, email, password_hash, role, is_active, created_at, updated_at)
          VALUES (?, 'admin', '<EMAIL>', ?, 'admin', 1, ?, ?)
        `).run(id, passwordHash, now, now);
  console.log('👤 Seeded default admin user (safety check)');
      }
    }
  } catch (e) {
    console.warn('Admin safety seed skipped:', (e as any)?.message);
  }

  } catch (error: any) {
    // Column already exists, ignore error
    if (!error.message.includes('duplicate column name')) {
      console.warn('Migration warning for expires_at column:', error.message);
    }
  }

  // Create indexes for better performance
  database.exec(`
    CREATE INDEX IF NOT EXISTS idx_executions_flow_id ON executions (flow_id);
    CREATE INDEX IF NOT EXISTS idx_executions_status ON executions (status);
    CREATE INDEX IF NOT EXISTS idx_executions_started_at ON executions (started_at);
    CREATE INDEX IF NOT EXISTS idx_execution_logs_execution_id ON execution_logs (execution_id);
    CREATE INDEX IF NOT EXISTS idx_execution_logs_timestamp ON execution_logs (timestamp);
    CREATE INDEX IF NOT EXISTS idx_schedules_flow_id ON schedules (flow_id);
    CREATE INDEX IF NOT EXISTS idx_schedules_enabled ON schedules (enabled);
    CREATE INDEX IF NOT EXISTS idx_schedules_next_run_at ON schedules (next_run_at);
    CREATE INDEX IF NOT EXISTS idx_credentials_type ON credentials (type);
    CREATE INDEX IF NOT EXISTS idx_credentials_name ON credentials (name);
  `);

  // Create trigger to update updated_at on flows
  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_flows_updated_at
    AFTER UPDATE ON flows
    FOR EACH ROW
    BEGIN
      UPDATE flows SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // Create trigger to update updated_at on schedules
  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_schedules_updated_at
    AFTER UPDATE ON schedules
    FOR EACH ROW
    BEGIN
      UPDATE schedules SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // Create trigger to update updated_at on credentials
  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_credentials_updated_at
    AFTER UPDATE ON credentials
    FOR EACH ROW
    BEGIN
      UPDATE credentials SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // Create trigger to update updated_at on customers
  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_customers_updated_at
    AFTER UPDATE ON customers
    FOR EACH ROW
    BEGIN
      UPDATE customers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  console.log('✅ Database schema initialized');
}

// Prepared statements will be defined after database initialization
let preparedStatements: any;

// Initialize database on module load
initializeDatabase();

// Define prepared statements after database initialization
preparedStatements = {
  // Flow statements
  insertFlow: database.prepare(`
    INSERT INTO flows (id, name, description, customer_id, steps, variables, settings, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  updateFlow: database.prepare(`
    UPDATE flows
    SET name = ?, description = ?, customer_id = ?, steps = ?, variables = ?, settings = ?, updated_at = ?
    WHERE id = ?
  `),

  getFlowById: database.prepare('SELECT * FROM flows WHERE id = ?'),
  getAllFlows: database.prepare('SELECT * FROM flows ORDER BY updated_at DESC'),
  getAllFlowsPaginated: database.prepare('SELECT * FROM flows ORDER BY updated_at DESC LIMIT ? OFFSET ?'),
  deleteFlow: database.prepare('DELETE FROM flows WHERE id = ?'),
  flowExists: database.prepare('SELECT 1 FROM flows WHERE id = ? LIMIT 1'),

  // Execution statements
  insertExecution: database.prepare(`
    INSERT INTO executions (id, flow_id, customer_id, status, started_at, results)
    VALUES (?, ?, ?, ?, ?, ?)
  `),

  updateExecutionStatus: database.prepare(`
    UPDATE executions
    SET status = ?, completed_at = ?, error = ?
    WHERE id = ?
  `),

  updateExecutionResults: database.prepare(`
    UPDATE executions
    SET results = ?
    WHERE id = ?
  `),

  getExecutionById: database.prepare('SELECT * FROM executions WHERE id = ?'),
  getExecutionsByFlowId: database.prepare('SELECT * FROM executions WHERE flow_id = ? ORDER BY started_at DESC'),
  getAllExecutions: database.prepare('SELECT * FROM executions ORDER BY started_at DESC LIMIT ? OFFSET ?'),
  getExecutionsByStatus: database.prepare('SELECT * FROM executions WHERE status = ? ORDER BY started_at DESC LIMIT ? OFFSET ?'),
  deleteExecution: database.prepare('DELETE FROM executions WHERE id = ?'),

  // Execution log statements
  insertExecutionLog: database.prepare(`
    INSERT INTO execution_logs (execution_id, timestamp, level, message, step_id, data)
    VALUES (?, ?, ?, ?, ?, ?)
  `),

  getExecutionLogs: database.prepare(`
    SELECT * FROM execution_logs
    WHERE execution_id = ?
    ORDER BY timestamp ASC
  `),

  deleteExecutionLogs: database.prepare('DELETE FROM execution_logs WHERE execution_id = ?'),

  // Schedule statements
  insertSchedule: database.prepare(`
    INSERT INTO schedules (id, flow_id, name, description, cron_expression, timezone, enabled, variables, created_at, updated_at, next_run_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  updateSchedule: database.prepare(`
    UPDATE schedules
    SET name = ?, description = ?, cron_expression = ?, timezone = ?, enabled = ?, variables = ?, updated_at = ?, next_run_at = ?
    WHERE id = ?
  `),

  updateScheduleLastRun: database.prepare(`
    UPDATE schedules
    SET last_run_at = ?, next_run_at = ?
    WHERE id = ?
  `),

  getScheduleById: database.prepare('SELECT * FROM schedules WHERE id = ?'),
  getSchedulesByFlowId: database.prepare('SELECT * FROM schedules WHERE flow_id = ? ORDER BY created_at DESC'),
  getAllSchedules: database.prepare('SELECT * FROM schedules ORDER BY created_at DESC'),
  getEnabledSchedules: database.prepare('SELECT * FROM schedules WHERE enabled = 1 ORDER BY next_run_at ASC'),
  getSchedulesDueForExecution: database.prepare('SELECT * FROM schedules WHERE enabled = 1 AND next_run_at <= ? ORDER BY next_run_at ASC'),
  deleteSchedule: database.prepare('DELETE FROM schedules WHERE id = ?'),
  scheduleExists: database.prepare('SELECT 1 FROM schedules WHERE id = ? LIMIT 1'),

  // Credential statements
  insertCredential: database.prepare(`
    INSERT INTO credentials (id, name, description, type, username, encrypted_password, encrypted_secret, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  updateCredential: database.prepare(`
    UPDATE credentials
    SET name = ?, description = ?, username = ?, encrypted_password = ?, encrypted_secret = ?, updated_at = ?
    WHERE id = ?
  `),

  getCredentialById: database.prepare('SELECT * FROM credentials WHERE id = ?'),
  getAllCredentials: database.prepare('SELECT * FROM credentials ORDER BY name ASC'),
  getCredentialsByType: database.prepare('SELECT * FROM credentials WHERE type = ? ORDER BY name ASC'),
  deleteCredential: database.prepare('DELETE FROM credentials WHERE id = ?'),
  credentialExists: database.prepare('SELECT 1 FROM credentials WHERE id = ? LIMIT 1'),

  // Customer statements
  insertCustomer: database.prepare(`
    INSERT INTO customers (id, customer_number, name, visma_number, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?)
  `),

  updateCustomer: database.prepare(`
    UPDATE customers
    SET customer_number = ?, name = ?, visma_number = ?, updated_at = ?
    WHERE id = ?
  `),


	  // User statements
	  insertUser: database.prepare(`
	    INSERT INTO users (id, username, email, password_hash, role, is_active, created_at, updated_at)
	    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	  `),
	  updateUserRecord: database.prepare(`
	    UPDATE users
	    SET username = ?, email = ?, role = ?, is_active = ?, updated_at = ?
	    WHERE id = ?
	  `),
	  updateUserPassword: database.prepare(`
	    UPDATE users
	    SET password_hash = ?, updated_at = ?
	    WHERE id = ?
	  `),
	  updateUserLastLogin: database.prepare(`
	    UPDATE users
	    SET last_login_at = ?, updated_at = ?
	    WHERE id = ?
	  `),
	  getUserByIdRecord: database.prepare('SELECT * FROM users WHERE id = ?'),
	  getUserByUsernameOrEmail: database.prepare('SELECT * FROM users WHERE LOWER(username) = LOWER(?) OR LOWER(email) = LOWER(?)'),
	  getAllUsersRecords: database.prepare('SELECT id, username, email, role, is_active as isActive, created_at as createdAt, updated_at as updatedAt, last_login_at as lastLoginAt FROM users ORDER BY username ASC'),
	  userExistsByUsernameOrEmail: database.prepare('SELECT 1 FROM users WHERE username = ? OR email = ? LIMIT 1'),

  getCustomerById: database.prepare('SELECT * FROM customers WHERE id = ?'),
  getAllCustomers: database.prepare('SELECT * FROM customers ORDER BY name ASC'),
  getAllCustomersPaginated: database.prepare('SELECT * FROM customers ORDER BY name ASC LIMIT ? OFFSET ?'),
  searchCustomers: database.prepare(`
    SELECT * FROM customers
    WHERE LOWER(name) LIKE ? OR LOWER(customer_number) LIKE ?
    ORDER BY name ASC
  `),
  searchCustomersPaginated: database.prepare(`
    SELECT * FROM customers
    WHERE LOWER(name) LIKE ? OR LOWER(customer_number) LIKE ?
    ORDER BY name ASC
    LIMIT ? OFFSET ?
  `),
  getCustomerByNumber: database.prepare('SELECT * FROM customers WHERE customer_number = ?'),
  deleteCustomer: database.prepare('DELETE FROM customers WHERE id = ?'),
  customerExists: database.prepare('SELECT 1 FROM customers WHERE id = ? LIMIT 1'),
  customerNumberExists: database.prepare('SELECT 1 FROM customers WHERE customer_number = ? AND id != ? LIMIT 1'),

  // Token usage logs
  insertTokenUsageLog: database.prepare(`
    INSERT INTO token_usage_logs (timestamp, operation, model, provider, prompt_tokens, completion_tokens, total_tokens, step_id, user_id, metadata)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),
  getTokenUsageLogsSince: database.prepare(`
    SELECT * FROM token_usage_logs WHERE timestamp >= ? ORDER BY timestamp DESC
  `),
  getTokenUsageLogs: database.prepare(`
    SELECT * FROM token_usage_logs ORDER BY timestamp DESC LIMIT ? OFFSET ?
  `),
  getTokenUsageSummaryByOperationSince: database.prepare(`
    SELECT operation, COUNT(*) as operationCount, SUM(total_tokens) as totalTokens, SUM(prompt_tokens) as promptTokens, SUM(completion_tokens) as completionTokens
    FROM token_usage_logs
    WHERE timestamp >= ?
    GROUP BY operation
    ORDER BY totalTokens DESC
  `),
  getTokenUsageSummaryByModelSince: database.prepare(`
    SELECT model, COUNT(*) as operationCount, SUM(total_tokens) as totalTokens, SUM(prompt_tokens) as promptTokens, SUM(completion_tokens) as completionTokens
    FROM token_usage_logs
    WHERE timestamp >= ?
    GROUP BY model
    ORDER BY totalTokens DESC
  `),

  // Customer token statements
  insertCustomerToken: database.prepare(`
    INSERT INTO customer_tokens (id, customer_id, name, description, provider, encrypted_api_token, encrypted_refresh_token, expires_at, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  updateCustomerToken: database.prepare(`
    UPDATE customer_tokens
    SET name = ?, description = ?, provider = ?, encrypted_api_token = ?, encrypted_refresh_token = ?, expires_at = ?, updated_at = ?
    WHERE id = ?
  `),

  getCustomerTokenById: database.prepare('SELECT * FROM customer_tokens WHERE id = ?'),
  getCustomerTokensByCustomerId: database.prepare('SELECT * FROM customer_tokens WHERE customer_id = ? ORDER BY name ASC'),
  deleteCustomerToken: database.prepare('DELETE FROM customer_tokens WHERE id = ?'),
  deleteCustomerTokensByCustomerId: database.prepare('DELETE FROM customer_tokens WHERE customer_id = ?'),
  customerTokenExists: database.prepare('SELECT 1 FROM customer_tokens WHERE id = ? LIMIT 1'),
  customerTokenNameExists: database.prepare('SELECT 1 FROM customer_tokens WHERE customer_id = ? AND name = ? AND id != ? LIMIT 1')
};

// Export statements with explicit type
export const statements = {
  ...preparedStatements,
  database
} as any;

// Export database instance for advanced queries (already exported above)
// export { db };

// Graceful shutdown
process.on('exit', () => {
  database.close();
});

process.on('SIGINT', () => {
  database.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  database.close();
  process.exit(0);
});

console.log('✅ Database initialized successfully');
