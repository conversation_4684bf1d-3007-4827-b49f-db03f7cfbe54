import { Base<PERSON><PERSON>rovider } from '../BaseLLMProvider';
import { ChatCompletionRequest, ChatCompletionResponse } from '../interfaces';
import { ModelRegistry } from '../ModelRegistry';

export class AzureOpenAIProvider extends BaseLLMProvider {
  name = 'azure';
  private endpoint: string | null = null;
  private apiKey: string | null = null;
  private apiVersion: string | null = null;

  constructor() {
    super();

    // Only initialize if all required env vars are present
    if (this.isConfigured()) {
      this.endpoint = process.env.AZURE_OPENAI_ENDPOINT!;
      this.apiKey = process.env.AZURE_OPENAI_API_KEY!;
      this.apiVersion = process.env.AZURE_OPENAI_API_VERSION!;
    }
  }

  isConfigured(): boolean {
    const hasKey = !!process.env.AZURE_OPENAI_API_KEY;
    const hasEndpoint = !!process.env.AZURE_OPENAI_ENDPOINT;
    const hasVersion = !!process.env.AZURE_OPENAI_API_VERSION;

    console.log('🔍 Azure OpenAI Configuration Check:');
    console.log('- API Key:', hasKey ? 'Present' : 'Missing');
    console.log('- Endpoint:', hasEndpoint ? 'Present' : 'Missing');
    console.log('- Version:', hasVersion ? 'Present' : 'Missing');
    console.log('- All configured:', hasKey && hasEndpoint && hasVersion);

    return hasKey && hasEndpoint && hasVersion;
  }

  getSupportedModels(): string[] {
    return ModelRegistry.getModelsForProvider('azure').map(m => m.id);
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    if (!this.endpoint || !this.apiKey || !this.apiVersion) {
      throw new Error('Azure OpenAI not configured. Check your environment variables.');
    }

    const modelConfig = ModelRegistry.getModel(request.model, 'azure');
    if (!modelConfig) {
      throw new Error(`Unsupported model: ${request.model} for Azure provider`);
    }

    // Build the full URL based on your working endpoint structure
    const url = `${this.endpoint}/openai/deployments/${modelConfig.providerModel}/chat/completions?api-version=${this.apiVersion}`;

    console.log(`🌐 Making Azure OpenAI request to: ${url}`);
    console.log(`🔑 Using deployment: ${modelConfig.providerModel}`);

    // o3-mini has different parameter requirements
    const isO3Model = modelConfig.providerModel.includes('o3');
    const tokenParams = isO3Model
      ? { max_completion_tokens: request.maxTokens }
      : { max_tokens: request.maxTokens };

    // o3-mini doesn't support temperature parameter
    const temperatureParams = isO3Model
      ? {}
      : { temperature: request.temperature };

    const body = {
      messages: request.messages,
      ...temperatureParams,
      ...tokenParams
      // Note: Azure uses deployment name in URL, not in body
    };

    console.log(`📤 Request body:`, JSON.stringify(body, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': this.apiKey
      },
      body: JSON.stringify(body)
    });

    console.log(`📥 Response status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Error response: ${errorText}`);
      throw new Error(`Azure OpenAI API error: ${response.status} - ${errorText}`);
    }

    const completion: any = await response.json();
    console.log(`✅ Response received:`, JSON.stringify(completion, null, 2));

    const content = completion.choices?.[0]?.message?.content;
    if (!content) {
      throw new Error('No response content from Azure OpenAI');
    }

    return {
      content,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      } : undefined
    };
  }
}
