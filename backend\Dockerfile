# Multi-stage build for better optimization
# Build stage
FROM node:20-alpine as builder

# Set working directory
WORKDIR /app

# Install build dependencies for native modules
RUN apk add --no-cache python3 make g++

# Copy root tsconfig (backend extends this)
COPY tsconfig.json ./

# --- Build shared workspace ---
WORKDIR /app/shared
COPY shared/package*.json ./
RUN npm ci
COPY shared/ ./
RUN npm run build

# --- Build backend workspace ---
WORKDIR /app/backend
COPY backend/package*.json ./
RUN npm ci
COPY backend/ ./
RUN npm run build

# Production stage
FROM mcr.microsoft.com/playwright:v1.55.0-jammy

# Create non-root user for security
RUN groupadd -r rpauser && useradd -r -g rpauser -m -d /home/<USER>

# Set working directory
WORKDIR /app

# Prepare shared package (built) for file: dependency
COPY --from=builder /app/shared/dist ./shared/dist
COPY --from=builder /app/shared/package.json ./shared/

# Copy backend package files and install production dependencies
COPY backend/package*.json ./backend/
WORKDIR /app/backend
RUN npm ci --omit=dev && npm cache clean --force

# Copy built backend
COPY --from=builder /app/backend/dist /app/backend/dist
COPY --from=builder /app/backend/package.json /app/backend/

# Copy startup script for Xvfb support
COPY backend/start-with-xvfb.sh /app/start-with-xvfb.sh

# Create directories with proper permissions and make startup script executable
RUN mkdir -p /app/screenshots /app/downloads /app/data && \
    chmod +x /app/start-with-xvfb.sh && \
    chown -R rpauser:rpauser /app

# Install Chromium browser and dependencies
RUN npx playwright install chromium && \
    npx playwright install-deps chromium

# Set non-interactive front-end and default timezone to avoid tzdata prompts
ENV DEBIAN_FRONTEND=noninteractive
# TZ can be set via build-arg or compose env; default via base image
# Install tzdata and packages for Xvfb + x11vnc + fluxbox + noVNC
RUN apt-get update && \
    apt-get install -y --no-install-recommends tzdata curl xvfb x11vnc fluxbox x11-utils git python3 && \
    if [ -n "${TZ:-}" ]; then ln -fs /usr/share/zoneinfo/$TZ /etc/localtime && dpkg-reconfigure --frontend noninteractive tzdata; fi && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install noVNC for web-based VNC access
RUN git clone https://github.com/novnc/noVNC.git /opt/noVNC && \
    git clone https://github.com/novnc/websockify /opt/noVNC/utils/websockify && \
    ln -s /opt/noVNC/vnc.html /opt/noVNC/index.html

# Switch to non-root user
USER rpauser

# Expose ports (API + VNC + noVNC web interface)
EXPOSE 44300
EXPOSE 5900
EXPOSE 6080

# Set environment variables
ENV NODE_ENV=production
ENV REDIS_URL=redis://redis:6379
ENV HOME=/home/<USER>
ENV npm_config_cache=/tmp/.npm

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f -k https://localhost:44300/health || exit 1

# Start the application with Xvfb support
WORKDIR /app
CMD ["./start-with-xvfb.sh"]
