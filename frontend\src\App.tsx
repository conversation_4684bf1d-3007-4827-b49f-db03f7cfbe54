import { Routes, Route, useLocation } from 'react-router-dom'
import { Layout } from './components/Layout'
import { AuthWrapper } from './components/AuthWrapper'
import { Dashboard } from './pages/Dashboard'
import { FlowList } from './pages/FlowList'
import { FlowEditor } from './pages/FlowEditor'
import { ExecutionList } from './pages/ExecutionList'
import { ExecutionDetails } from './pages/ExecutionDetails'
import { Schedules } from './pages/Schedules'
import { Customers } from './pages/Customers'
import { CustomerDetail } from './pages/CustomerDetail'
import { Credentials } from './pages/Credentials'
import { Settings } from './pages/Settings'
import TokenUsage from './pages/TokenUsage'

function App() {
  const location = useLocation()

  return (
    <AuthWrapper>
      <Layout>
        <Routes location={location} key={location.pathname}>
          <Route path="/" element={<Dashboard />} />
          <Route path="/flows" element={<FlowList />} />
          <Route path="/flows/new" element={<FlowEditor />} />
          <Route path="/flows/:id" element={<FlowEditor />} />
          <Route path="/executions" element={<ExecutionList />} />
          <Route path="/executions/:id" element={<ExecutionDetails />} />
          <Route path="/schedules" element={<Schedules />} />
          <Route path="/schedules/new/:flowId" element={<Schedules />} />
          <Route path="/customers" element={<Customers />} />
          <Route path="/customers/:id" element={<CustomerDetail />} />
          <Route path="/credentials" element={<Credentials />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/token-usage" element={<TokenUsage />} />
        </Routes>
      </Layout>
    </AuthWrapper>
  )
}

export default App
