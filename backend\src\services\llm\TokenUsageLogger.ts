import { TokenUsage } from './interfaces';

export interface TokenUsageLog {
  timestamp: Date;
  operation: string;
  model: string;
  provider: string;
  usage: TokenUsage;
  stepId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

import { statements } from '../../database/database';

export class TokenUsageLogger {
  private static logs: TokenUsageLog[] = [];
  private static maxLogs = 1000; // Keep last 1000 logs in memory

  /**
   * Log token usage for an LLM operation
   */
  static log(params: {
    operation: string;
    model: string;
    provider: string;
    usage: TokenUsage;
    stepId?: string;
    userId?: string;
    metadata?: Record<string, any>;
  }): void {
    const logEntry: TokenUsageLog = {
      timestamp: new Date(),
      operation: params.operation,
      model: params.model,
      provider: params.provider,
      usage: params.usage,
      stepId: params.stepId,
      userId: params.userId,
      metadata: params.metadata
    };

    this.logs.push(logEntry);

    // Persist to database
    try {
      statements.insertTokenUsageLog.run(
        logEntry.timestamp.toISOString(),
        logEntry.operation,
        logEntry.model,
        logEntry.provider,
        logEntry.usage.promptTokens,
        logEntry.usage.completionTokens,
        logEntry.usage.totalTokens,
        logEntry.stepId || null,
        logEntry.userId || null,
        logEntry.metadata ? JSON.stringify(logEntry.metadata) : null
      );
    } catch (err) {
      console.error('Failed to persist token usage log:', err);
    }

    // Keep only the most recent logs in memory
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Log to console with emoji for easy identification
    console.log(
      `🔍 Token Usage - ${params.operation} | ` +
      `Model: ${params.model} | ` +
      `Provider: ${params.provider} | ` +
      `Prompt: ${params.usage.promptTokens} | ` +
      `Completion: ${params.usage.completionTokens} | ` +
      `Total: ${params.usage.totalTokens}` +
      (params.stepId ? ` | Step: ${params.stepId}` : '') +
      (params.userId ? ` | User: ${params.userId}` : '')
    );
  }

  /**
   * Get token usage statistics for a specific time period
   */
  static getUsageStats(options: {
    since?: Date;
    operation?: string;
    model?: string;
    provider?: string;
    userId?: string;
  } = {}): {
    totalTokens: number;
    promptTokens: number;
    completionTokens: number;
    operationCount: number;
    logs: TokenUsageLog[];
  } {
    // Fetch from DB (and also keep in-memory for quick recent access)
    let rows: any[];
    if (options.since) {
      rows = statements.getTokenUsageLogsSince.all(options.since.toISOString());
    } else {
      rows = statements.getTokenUsageLogs.all(1000, 0); // fallback: last 1000
    }

    // Transform to TokenUsageLog
    let filteredLogs: TokenUsageLog[] = rows.map(r => ({
      timestamp: new Date(r.timestamp),
      operation: r.operation,
      model: r.model,
      provider: r.provider,
      usage: {
        promptTokens: r.prompt_tokens,
        completionTokens: r.completion_tokens,
        totalTokens: r.total_tokens
      },
      stepId: r.step_id || undefined,
      userId: r.user_id || undefined,
      metadata: r.metadata ? JSON.parse(r.metadata) : undefined
    }));

    // Apply additional filters
    if (options.operation) {
      filteredLogs = filteredLogs.filter(log => log.operation === options.operation);
    }
    if (options.model) {
      filteredLogs = filteredLogs.filter(log => log.model === options.model);
    }
    if (options.provider) {
      filteredLogs = filteredLogs.filter(log => log.provider === options.provider);
    }
    if (options.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === options.userId);
    }

    // Calculate totals
    const totalTokens = filteredLogs.reduce((sum, log) => sum + log.usage.totalTokens, 0);
    const promptTokens = filteredLogs.reduce((sum, log) => sum + log.usage.promptTokens, 0);
    const completionTokens = filteredLogs.reduce((sum, log) => sum + log.usage.completionTokens, 0);

    return {
      totalTokens,
      promptTokens,
      completionTokens,
      operationCount: filteredLogs.length,
      logs: filteredLogs
    };
  }

  /**
   * Get usage summary by operation
   */
  static getUsageSummaryByOperation(since?: Date): Record<string, {
    totalTokens: number;
    promptTokens: number;
    completionTokens: number;
    operationCount: number;
  }> {
    const rows: any[] = since
      ? statements.getTokenUsageSummaryByOperationSince.all(since.toISOString())
      : statements.getTokenUsageSummaryByOperationSince.all(new Date(0).toISOString());

    const summary: Record<string, any> = {};
    rows.forEach(r => {
      summary[r.operation] = {
        totalTokens: Number(r.totalTokens) || 0,
        promptTokens: Number(r.promptTokens) || 0,
        completionTokens: Number(r.completionTokens) || 0,
        operationCount: Number(r.operationCount) || 0,
      };
    });

    return summary;
  }

  /**
   * Get usage summary by model
   */
  static getUsageSummaryByModel(since?: Date): Record<string, {
    totalTokens: number;
    promptTokens: number;
    completionTokens: number;
    operationCount: number;
  }> {
    const rows: any[] = since
      ? statements.getTokenUsageSummaryByModelSince.all(since.toISOString())
      : statements.getTokenUsageSummaryByModelSince.all(new Date(0).toISOString());

    const summary: Record<string, any> = {};
    rows.forEach(r => {
      summary[r.model] = {
        totalTokens: Number(r.totalTokens) || 0,
        promptTokens: Number(r.promptTokens) || 0,
        completionTokens: Number(r.completionTokens) || 0,
        operationCount: Number(r.operationCount) || 0,
      };
    });

    return summary;
  }

  /**
   * Clear all logs (useful for testing)
   */
  static clearLogs(): void {
    this.logs = [];
  }

  /**
   * Get all logs (for debugging)
   */
  static getAllLogs(): TokenUsageLog[] {
    return [...this.logs];
  }
}
