import { chromium, firefox, webkit, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext } from 'playwright';
import { RpaFlow, RpaStep, FlowSettings, ExecutionLog } from '@rpa-project/shared';
import { BaseRunner, RunnerContext, StepExecutionResult } from '../base';
import { STEP_RUNNER_MAPPING } from '../registry/stepTypes';
import {
  executeNavigate,
  executeGoBack,
  executeGoForward,
  executeReload,
  executeClick,
  executeFill,
  executeType,
  executeSelectOption,
  executeCheck,
  executeUncheck,
  executeFillPassword,
  executeFill2FA,
  executeExtractText,
  executeExtractAttribute,
  executeTakeScreenshot,
  executeDownloadFile,
  executeWaitForSelector,
  executeWaitForTimeout,
  executeWaitForUrl,
  executeIfElementExists,
  executeConditionalClick
} from './stepExecutors';

export interface PlaywrightRunnerContext {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
}

/**
 * Enhanced PlaywrightRunner with modular step executors
 */
export class PlaywrightRunner extends BaseRunner {
  private playwrightContext?: PlaywrightRunnerContext;

  async initialize(settings: FlowSettings, variables: Record<string, any> = {}): Promise<void> {

    const browserType = settings.browser || 'chromium';
    const headless = settings.headless !== false;

    // Check if we're running in Docker and need Xvfb for non-headless mode
    const isDocker = process.env.NODE_ENV === 'production' || process.env.DOCKER_ENV === 'true';
    const needsXvfb = !headless && isDocker;

    if (needsXvfb) {
      // Ensure DISPLAY is set; Xvfb is started by container entrypoint (start-with-xvfb.sh)
      if (!process.env.DISPLAY) {
        process.env.DISPLAY = ':99';
      }
      // Give a brief moment in case X server is still coming up
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return this.initializeBrowser(browserType, headless, settings, variables);
  }

  private async initializeBrowser(
    browserType: string,
    headless: boolean,
    settings: FlowSettings,
    variables: Record<string, any>
  ): Promise<void> {
    let browser: Browser;

    try {
      switch (browserType) {
        case 'firefox':
          browser = await firefox.launch({ headless });
          break;
        case 'webkit':
          browser = await webkit.launch({ headless });
          break;
        default:
          browser = await chromium.launch(
            headless
              ? { headless: true }
              : {
                  headless: false,
                  args: [
                    "--display=:99",
                    "--no-sandbox",
                    "--disable-dev-shm-usage"
                  ]
                }
          );
      }
    } catch (error: any) {
      // If non-headless mode fails and we're in Docker, try headless mode
      if (!headless && (process.env.NODE_ENV === 'production' || process.env.DOCKER_ENV === 'true')) {
        console.warn('Non-headless browser launch failed, falling back to headless mode:', error.message);
        console.log('🔄 Retrying with headless mode...');

        switch (browserType) {
          case 'firefox':
            browser = await firefox.launch({ headless: true });
            break;
          case 'webkit':
            browser = await webkit.launch({ headless: true });
            break;
          default:
            browser = await chromium.launch({ headless: true });
        }
      } else {
        throw error;
      }
    }

    const contextOptions: any = {};

    if (settings.viewport) {
      contextOptions.viewport = settings.viewport;
    }

    if (settings.userAgent) {
      contextOptions.userAgent = settings.userAgent;
    }

    if (settings.locale) {
      contextOptions.locale = settings.locale;
    }

    if (settings.timezone) {
      contextOptions.timezoneId = settings.timezone;
    }

    const context = await browser.newContext(contextOptions);
    const page = await context.newPage();

    this.playwrightContext = {
      browser,
      context,
      page,
      variables: { ...variables },
      onLog: this.logHandler
    };
  }

  getSupportedStepTypes(): string[] {
    return Object.keys(STEP_RUNNER_MAPPING).filter(
      stepType => STEP_RUNNER_MAPPING[stepType as keyof typeof STEP_RUNNER_MAPPING] === 'playwright'
    );
  }



  /**
   * Override executeFlow to use the base implementation without custom delays
   * All delays are now handled by FlowExecutor
   */
  async executeFlow(flow: RpaFlow, variables: Record<string, any> = {}): Promise<Record<string, any>> {
    return super.executeFlow(flow, variables);
  }

  async executeStep(step: RpaStep, context: RunnerContext, stepIndex?: number): Promise<StepExecutionResult> {
    if (!this.playwrightContext) {
      throw new Error('PlaywrightRunner not initialized');
    }

    // Delays are now handled by FlowExecutor for consistency

    const { page, variables, onLog } = this.playwrightContext;

    onLog({
      level: 'info',
      message: `Executing step: ${step.type}`,
      stepId: step.id
    });

    try {
      // Create executor context
      const executorContext = {
        page,
        variables: context.variables,
        onLog,
        interpolateVariables: this.interpolateVariables.bind(this),
        executeStep: this.executeStep.bind(this),
        flowId: context.flowId
      };

      // Route to appropriate step executor
      switch (step.type) {
        // Navigation steps
        case 'navigate':
          return await executeNavigate(step as any, executorContext);
        case 'goBack':
          return await executeGoBack(step, executorContext);
        case 'goForward':
          return await executeGoForward(step, executorContext);
        case 'reload':
          return await executeReload(step, executorContext);

        // Interaction steps
        case 'click':
          return await executeClick(step as any, executorContext);
        case 'fill':
          return await executeFill(step as any, executorContext);
        case 'type':
          return await executeType(step as any, executorContext);
        case 'selectOption':
          return await executeSelectOption(step as any, executorContext);
        case 'check':
          return await executeCheck(step as any, executorContext);
        case 'uncheck':
          return await executeUncheck(step as any, executorContext);
        case 'fillPassword':
          return await executeFillPassword(step as any, executorContext);
        case 'fill2FA':
          return await executeFill2FA(step as any, executorContext);

        // Extraction steps
        case 'extractText':
          return await executeExtractText(step as any, executorContext, stepIndex);
        case 'extractAttribute':
          return await executeExtractAttribute(step as any, executorContext, stepIndex);
        case 'takeScreenshot':
          return await executeTakeScreenshot(step as any, executorContext, stepIndex);
        case 'downloadFile':
          return await executeDownloadFile(step as any, executorContext, stepIndex);

        // Waiting steps
        case 'waitForSelector':
          return await executeWaitForSelector(step as any, executorContext);
        case 'waitForTimeout':
          return await executeWaitForTimeout(step as any, executorContext);
        case 'waitForUrl':
          return await executeWaitForUrl(step as any, executorContext);

        // Conditional steps
        case 'ifElementExists':
          return await executeIfElementExists(step as any, executorContext);
        case 'conditionalClick':
          return await executeConditionalClick(step as any, executorContext);

        default:
          return {
            success: false,
            error: `Unsupported step type: ${step.type}`
          };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onLog({
        level: 'error',
        message: `Error executing step ${step.type}: ${errorMessage}`,
        stepId: step.id
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async cleanup(): Promise<void> {
    if (this.playwrightContext) {
      try {
        await this.playwrightContext.context.close();
        await this.playwrightContext.browser.close();
      } catch (error) {
        console.error('Error during PlaywrightRunner cleanup:', error);
      }
      this.playwrightContext = undefined;
    }
  }

  /**
   * Generate random delay between 1000-5000ms for web automation steps
   */
  private getRandomDelay(): number {
    return Math.floor(Math.random() * (5000 - 1000 + 1)) + 1000;
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
