import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Allow Docker/dev override for backend URL
const backendTarget = process.env.BACKEND_URL || 'http://localhost:3002'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: true,
    port: 3000,
    hmr: { clientPort: 3000 },
    watch: { usePolling: true },
    proxy: {
      '/api': {
        target: backendTarget,
        changeOrigin: true,
        secure: false // Accept self-signed certificates
      }
    }
  }
})
