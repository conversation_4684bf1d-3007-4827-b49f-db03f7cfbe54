import dotenv from 'dotenv';

// Load environment variables first
dotenv.config();

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { flowRoutes } from './api/flows';
import { executionRoutes } from './api/executions';
import { queueRoutes } from './api/queue';
import { scheduleRoutes } from './api/schedules';
import customerRoutes from './api/customers';
import { credentialRoutes } from './api/credentials';
import { settingsRoutes } from './api/settings';
import { aiAssistantRoutes } from './api/ai-assistant';
import oauth2Routes from './api/oauth2';
import { authRoutes } from './api/auth';
import tokenUsageRoutes from './api/token-usage';
import { errorHandler } from './middleware/errorHandler';
import { securityLogger } from './middleware/auth';
import { globalRateLimit } from './middleware/rateLimiter';
import { applySecurity } from './middleware/security';
import { securityConfig, validateSecurityConfig, corsConfig, securityHeaders } from './config/security';
import { initializeQueue } from './queue/queue';
import { initializeScheduler, shutdownScheduler } from './queue/scheduler';
import { tokenRefreshJob } from './jobs/tokenRefreshJob';
// Import database to initialize it
import './database/database';
// Initialize runner factory
import { getRunnerFactory } from './runners/factory';

const app = express();
const PORT = process.env.PORT || 3002;

// Validate security configuration
try {
  validateSecurityConfig();
  console.log('✅ Security configuration validated');
} catch (error) {
  console.error('❌ Security configuration validation failed:', error);
  process.exit(1);
}

// Security middleware
app.use(helmet(securityHeaders));
app.use(cors(corsConfig));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
app.use(globalRateLimit);

// Security middleware
app.use(applySecurity());

// Security logging
app.use(securityLogger);

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/flows', flowRoutes);
app.use('/api/executions', executionRoutes);
app.use('/api/queue', queueRoutes);
app.use('/api/schedules', scheduleRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/credentials', credentialRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/ai-assistant', aiAssistantRoutes);
app.use('/api/oauth2', oauth2Routes);
app.use('/api/token-usage', tokenUsageRoutes);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('/*path', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

async function startServer() {
  try {
    // Database is already initialized via import
    console.log('✅ SQLite database initialized');

    // Initialize runner factory
    const runnerFactory = getRunnerFactory();
    console.log('✅ Runner factory initialized');

    // Initialize queue system
    await initializeQueue();
    console.log('✅ Queue system initialized');

    // Initialize scheduler
    await initializeScheduler();
    console.log('✅ Scheduler initialized');

    // Start token refresh job
    tokenRefreshJob.start();
    console.log('✅ Token refresh job started');

    // Start server
    app.listen(PORT, (error?: Error) => {
      if (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
      }
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`📖 API docs: http://localhost:${PORT}/api`);
      console.log(`💾 Database: SQLite (persistent storage)`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  tokenRefreshJob.stop();
  await shutdownScheduler();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  tokenRefreshJob.stop();
  await shutdownScheduler();
  process.exit(0);
});

startServer();
