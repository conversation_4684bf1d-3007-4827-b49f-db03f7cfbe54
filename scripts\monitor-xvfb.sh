#!/bin/bash

# Monitor Xvfb memory usage and system resources
# Usage: ./scripts/monitor-xvfb.sh [interval_seconds]

INTERVAL=${1:-10}
LOG_FILE="/var/log/xvfb-monitor.log"

echo "🔍 Starting Xvfb monitoring (interval: ${INTERVAL}s)"
echo "📝 Logging to: $LOG_FILE"

# Create log file if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# Function to get process info
get_process_info() {
    local process_name=$1
    local pid=$(pgrep -f "$process_name" | head -1)
    
    if [ -n "$pid" ]; then
        local mem_kb=$(ps -o rss= -p "$pid" 2>/dev/null | tr -d ' ')
        local mem_mb=$((mem_kb / 1024))
        local cpu=$(ps -o %cpu= -p "$pid" 2>/dev/null | tr -d ' ')
        echo "$pid,$mem_mb,$cpu"
    else
        echo "0,0,0"
    fi
}

# Function to log metrics
log_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Get process information
    local xvfb_info=$(get_process_info "Xvfb")
    local x11vnc_info=$(get_process_info "x11vnc")
    local fluxbox_info=$(get_process_info "fluxbox")
    local chrome_info=$(get_process_info "chromium\|chrome")
    
    # Parse process info
    IFS=',' read -r xvfb_pid xvfb_mem xvfb_cpu <<< "$xvfb_info"
    IFS=',' read -r vnc_pid vnc_mem vnc_cpu <<< "$x11vnc_info"
    IFS=',' read -r flux_pid flux_mem flux_cpu <<< "$fluxbox_info"
    IFS=',' read -r chrome_pid chrome_mem chrome_cpu <<< "$chrome_info"
    
    # System memory info
    local total_mem=$(free -m | awk 'NR==2{print $2}')
    local used_mem=$(free -m | awk 'NR==2{print $3}')
    local free_mem=$(free -m | awk 'NR==2{print $4}')
    local mem_percent=$(( (used_mem * 100) / total_mem ))
    
    # System load
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
    
    # Total X11 stack memory usage
    local x11_total_mem=$((xvfb_mem + vnc_mem + flux_mem + chrome_mem))
    
    # Log to file (CSV format)
    echo "$timestamp,$xvfb_pid,$xvfb_mem,$xvfb_cpu,$vnc_mem,$flux_mem,$chrome_mem,$x11_total_mem,$used_mem,$mem_percent,$load_avg" >> "$LOG_FILE"
    
    # Console output
    printf "\n🕐 %s\n" "$timestamp"
    printf "┌─────────────────────────────────────────────────────────────┐\n"
    printf "│ Process Monitoring                                          │\n"
    printf "├─────────────────────────────────────────────────────────────┤\n"
    printf "│ Xvfb:     PID=%-6s MEM=%-4s MB  CPU=%-6s%%           │\n" "$xvfb_pid" "$xvfb_mem" "$xvfb_cpu"
    printf "│ x11vnc:   PID=%-6s MEM=%-4s MB  CPU=%-6s%%           │\n" "$vnc_pid" "$vnc_mem" "$vnc_cpu"
    printf "│ fluxbox:  PID=%-6s MEM=%-4s MB  CPU=%-6s%%           │\n" "$flux_pid" "$flux_mem" "$flux_cpu"
    printf "│ Chrome:   PID=%-6s MEM=%-4s MB  CPU=%-6s%%           │\n" "$chrome_pid" "$chrome_mem" "$chrome_cpu"
    printf "├─────────────────────────────────────────────────────────────┤\n"
    printf "│ X11 Stack Total: %-4s MB                               │\n" "$x11_total_mem"
    printf "│ System Memory:   %-4s/%-4s MB (%-3s%%)                │\n" "$used_mem" "$total_mem" "$mem_percent"
    printf "│ Load Average:    %-8s                                 │\n" "$load_avg"
    printf "└─────────────────────────────────────────────────────────────┘\n"
    
    # Alert if memory usage is high
    if [ "$mem_percent" -gt 80 ]; then
        echo "⚠️  WARNING: High memory usage detected ($mem_percent%)" | tee -a "$LOG_FILE"
    fi
    
    if [ "$x11_total_mem" -gt 500 ]; then
        echo "⚠️  WARNING: X11 stack using high memory ($x11_total_mem MB)" | tee -a "$LOG_FILE"
    fi
}

# Signal handlers for graceful shutdown
cleanup() {
    echo ""
    echo "🛑 Monitoring stopped"
    echo "📊 Log file: $LOG_FILE"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Write CSV header
if [ ! -s "$LOG_FILE" ]; then
    echo "timestamp,xvfb_pid,xvfb_mem_mb,xvfb_cpu,vnc_mem_mb,flux_mem_mb,chrome_mem_mb,x11_total_mb,system_used_mb,system_mem_percent,load_avg" > "$LOG_FILE"
fi

# Main monitoring loop
while true; do
    log_metrics
    sleep "$INTERVAL"
done
