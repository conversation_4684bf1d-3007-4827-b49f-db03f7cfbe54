# Build stage
FROM node:20-bookworm-slim as build

WORKDIR /app
RUN npm config set ignore-scripts false
RUN npm i -g npm@11.5.2

# Copy package files
COPY package.json ./
COPY frontend/package.json ./frontend/
COPY shared/package.json ./shared/

# Install dependencies
RUN rm -f package-lock.json && npm install --include=optional
RUN npm rebuild lightningcss --verbose

# Copy shared types
COPY shared/ ./shared/
COPY tsconfig.json ./

# Build shared package
WORKDIR /app/shared
RUN npm run build

# Copy frontend source
WORKDIR /app
RUN npm config set ignore-scripts false
RUN npm i -g npm@11.5.2
COPY frontend/ ./frontend/

# Build frontend
WORKDIR /app/frontend
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built files
COPY --from=build /app/frontend/dist /usr/share/nginx/html

# Copy nginx configuration
COPY frontend/nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

# Start nginx
CMD ["/bin/sh", "-lc", "nginx -g 'daemon off;'" ]
