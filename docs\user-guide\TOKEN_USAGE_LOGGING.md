# Token-användningsloggning

Denna guide beskriver hur token-användning för LLM-operationer loggas och övervakas i systemet.

## Översikt

Systemet loggar automatiskt token-förbrukning för alla LLM-operationer, inklusive:
- Prompt tokens (indata)
- Completion tokens (utdata)
- Total token-användning
- Modell och provider-information
- Metadata om operationen

## Automatisk loggning

### Backend-loggning

Token-användning loggas automatiskt på två nivåer:

1. **LLMService-nivå**: Alla LLM-anrop loggas centralt
2. **Steg-nivå**: Specifika operationer loggas med kontext

### Loggade operationer

- `processWithLLM` - Textbearbetning med LLM
- `extractPdfValues` - PDF-värdeextraktion
- `create_flow` - AI-assisterad flödeskapande
- `suggest_steps` - Stegförslag från AI
- `flow_optimization` - Flödesoptimering
- `debug_flow` - Felsökning av flöden
- `edit_step` - Stegredering
- `fortnox_ai_processing` - Fortnox AI-bearbetning

## Konsollutskrifter

Token-användning visas i konsollen med emoji för enkel identifiering:

```
🔍 Token Usage - processWithLLM | Model: gpt-4o-mini | Provider: openai | Prompt: 150 | Completion: 75 | Total: 225 | Step: step-123
```

## API-endpoints

### Hämta statistik

```http
GET /api/token-usage/stats?since=2024-01-01&operation=processWithLLM
```

**Query-parametrar:**
- `since` - Datum att filtrera från (ISO-format)
- `operation` - Specifik operation att filtrera på
- `model` - Specifik modell att filtrera på
- `provider` - Specifik provider att filtrera på
- `userId` - Specifik användare att filtrera på

### Dashboard-data

```http
GET /api/token-usage/dashboard?since=24h
```

**Stöder relativa tider:**
- `24h` - Senaste 24 timmarna
- `7d` - Senaste 7 dagarna
- `30d` - Senaste 30 dagarna

### Sammanfattning per operation

```http
GET /api/token-usage/summary/operations?since=2024-01-01
```

### Sammanfattning per modell

```http
GET /api/token-usage/summary/models?since=2024-01-01
```

### Senaste loggar

```http
GET /api/token-usage/logs?limit=100
```

## Frontend-gränssnitt

### Token-användningssida

Tillgänglig på `/token-usage` i webbgränssnittet.

**Funktioner:**
- Översikt över total token-användning
- Uppdelning per operation och modell
- Tidsfiltrering (24h, 7d, 30d)
- Senaste operationer
- Realtidsuppdatering

### Navigering

Token-användningssidan finns i huvudnavigationen med en blixt-ikon (⚡).

## Datastruktur

### TokenUsageLog

```typescript
interface TokenUsageLog {
  timestamp: Date;
  operation: string;
  model: string;
  provider: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  stepId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}
```

### Metadata-exempel

```typescript
{
  temperature: 0.3,
  maxTokens: 2000,
  messageCount: 2,
  optimizationGoal: "speed",
  stepsCount: 5
}
```

## Konfiguration

### Minnesbegränsning

TokenUsageLogger håller de senaste 1000 loggarna i minnet. Äldre loggar rensas automatiskt.

### Rensa loggar

```http
DELETE /api/token-usage/logs
```

**Obs:** Denna endpoint bör skyddas med admin-behörighet i produktion.

## Användningsfall

### Kostnadsövervakning

Övervaka token-användning för att:
- Uppskatta API-kostnader
- Identifiera kostsamma operationer
- Optimera prompt-design

### Prestandaanalys

Analysera token-användning för att:
- Identifiera ineffektiva operationer
- Jämföra olika modeller
- Optimera temperatur och max-tokens

### Felsökning

Använd token-loggar för att:
- Spåra specifika operationer
- Identifiera ovanliga användningsmönster
- Debugga LLM-relaterade problem

## Säkerhet

### Dataskydd

- Token-loggar innehåller inte känslig användardata
- Endast metadata och användningsstatistik lagras
- Prompt-innehåll loggas inte

### Åtkomstskydd

I produktion bör token-användnings-endpoints skyddas med:
- Autentisering
- Rollbaserad åtkomst
- Rate limiting

## Exempel på användning

### Övervaka daglig användning

```bash
curl "/api/token-usage/dashboard?since=24h"
```

### Hitta mest använda operationer

```bash
curl "/api/token-usage/summary/operations?since=7d"
```

### Spåra specifik modell

```bash
curl "/api/token-usage/stats?model=gpt-4o-mini&since=2024-01-01"
```

## Felsökning

### Inga loggar visas

1. Kontrollera att LLM-provider returnerar usage-data
2. Verifiera att TokenUsageLogger importeras korrekt
3. Kontrollera konsollen för fel

### Felaktiga siffror

1. Kontrollera tidszonsinställningar
2. Verifiera filtreringsparametrar
3. Kontrollera att alla LLM-anrop loggas

### Prestandaproblem

1. Minska antalet loggar i minnet
2. Implementera databaspersistens för stora volymer
3. Lägg till indexering för snabbare frågor
