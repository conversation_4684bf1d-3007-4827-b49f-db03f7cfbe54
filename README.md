# RPA Project

Ett komplett RPA-projekt byggt med TypeScript, React Flow, och Playwright för automatisering av webbprocesser.

## Snabbstart

```bash
# Installera dependencies
npm run install:all

# Starta Redis (krävs)
docker-compose up -d redis

# Starta utvecklingsservrar
npm run dev
npm run docker:dev

# Log
npm run docker:dev:logs

# Stoppa
npm run docker:dev:down

```

Applikationen startar på:
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:3000

## Dokumentation

All dokumentation finns organiserad i `docs/` mappen:

### 📁 [Project Documentation](docs/project/)
- [Projektöversikt & Arkitektur](docs/project/README.md)
- [Changelog & Versionshistorik](docs/project/CHANGELOG.md)

### 📁 [Development Documentation](docs/development/)
- [Arkitektur & Design](docs/development/architecture.md)
- [Lägga till nya steg](docs/development/adding-new-steps.md)
- [Lägga till nya runners](docs/development/adding-new-runners.md)
- [Felsökning](docs/development/troubleshooting.md)
- [Kodkonventioner](docs/development/conventions.md)
- [Build System](docs/development/build-system.md)
- [Migration Plan](docs/development/MIGRATION_PLAN.md)
- [Design System](docs/development/design-system-documentation.md)

### 📁 [User Guide](docs/user-guide/)
- [LLM Provider Configuration](docs/user-guide/LLM_PROVIDER_CONFIGURATION.md)
- [OAuth2 Setup](docs/user-guide/OAuth2_SETUP.md)
- [AI Assistant Guide](docs/user-guide/AI_ASSISTANT_README.md)

### 📁 [Examples & Templates](docs/examples/)
- Exempel på custom runners och steg
- Integration examples

### 📁 [Templates](docs/templates/)
- Mallar för nya steg och runners

## Teknisk Stack

- **Frontend**: React + TypeScript + React Flow + Vite
- **Backend**: Node.js + Express + TypeScript + Playwright
- **Queue**: BullMQ + Redis
- **Database**: Better-SQLite3
- **AI**: Flexibel LLM provider-arkitektur (OpenAI, Azure OpenAI)

## Licens

[Lägg till licensinformation här]

## Change owner
Code base changed owner to EbitApps
