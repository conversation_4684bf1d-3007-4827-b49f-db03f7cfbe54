/**
 * Authentication service for frontend
 */

export interface AuthUser {
  id: string;
  username: string;
  role: 'admin' | 'user' | 'operator' | 'viewer' | 'api';
}

export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  token: string | null;
}

class AuthService {
  private static instance: AuthService;
  private authState: AuthState = {
    isAuthenticated: false,
    user: null,
    token: null
  };

  private listeners: Array<(state: AuthState) => void> = [];

  private constructor() {
    // Load auth state from localStorage on initialization
    this.loadAuthState();
  }

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Load authentication state from localStorage
   */
  private loadAuthState(): void {
    try {
      const savedToken = localStorage.getItem('rpa_auth_token');
      const savedUser = localStorage.getItem('rpa_auth_user');

      if (savedToken && savedUser) {
        this.authState = {
          isAuthenticated: true,
          token: savedToken,
          user: JSON.parse(savedUser)
        };
        console.log('🔐 Auth state loaded from localStorage');
      }
    } catch (error) {
      console.error('Failed to load auth state:', error);
    }
  }



  /**
   * Save authentication state to localStorage
   */
  private saveAuthState(): void {
    if (this.authState.token && this.authState.user) {
      localStorage.setItem('rpa_auth_token', this.authState.token);
      localStorage.setItem('rpa_auth_user', JSON.stringify(this.authState.user));
    } else {
      localStorage.removeItem('rpa_auth_token');
      localStorage.removeItem('rpa_auth_user');
    }
  }

  /**
   * Notify all listeners of auth state changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.authState));
  }

  /**
   * Subscribe to auth state changes
   */
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Get current authentication state
   */
  getAuthState(): AuthState {
    return { ...this.authState };
  }

  /**
   * Get current authentication token
   */
  getToken(): string | null {
    return this.authState.token;
  }

  /**
   * Get current user
   */
  getUser(): AuthUser | null {
    return this.authState.user;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.authState.isAuthenticated && !!this.authState.token;
  }

  /**
   * Login with username and password
   */
  async login(username: string, password: string): Promise<void> {
    // Prefer explicit API base to avoid proxy issues; fallback to relative '/api' for local Vite proxy
    const API_BASE = (import.meta as any)?.env?.VITE_API_BASE || '';

    try {
      const response = await fetch(`${API_BASE}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        let errMsg = `Login failed (${response.status})`;
        try {
          const ct = response.headers.get('content-type') || '';
          if (ct.includes('application/json')) {
            const errorData = await response.json();
            errMsg = errorData?.error || errMsg;
          } else {
            const text = await response.text();
            if (text) errMsg = text;
          }
        } catch {}
        throw new Error(errMsg);
      }

      let data: any;
      try {
        data = await response.json();
      } catch {
        throw new Error('Invalid response from server');
      }

      if (!data?.success) {
        throw new Error(data?.error || 'Login failed');
      }

      // Set authentication state
      this.authState = {
        isAuthenticated: true,
        user: data.data.user,
        token: data.data.token
      };

      this.saveAuthState();
      this.notifyListeners();

    } catch (error) {
      console.error('🔐 Login failed:', error);
      throw error;
    }
  }

  /**
   * Logout user
   */
  logout(): void {
    this.authState = {
      isAuthenticated: false,
      user: null,
      token: null
    };

    this.saveAuthState();
    this.notifyListeners();


  }

  /**
   * Set authentication state (for manual login)
   */
  setAuth(user: AuthUser, token: string): void {
    this.authState = {
      isAuthenticated: true,
      user,
      token
    };

    this.saveAuthState();
    this.notifyListeners();


  }

  /**
   * Check if user has specific role
   */
  hasRole(role: AuthUser['role']): boolean {
    return this.authState.user?.role === role;
  }

  /**
   * Check if user has admin privileges
   */
  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  /**
   * Check if user can perform operations
   */
  canOperate(): boolean {
    return this.hasRole('admin') || this.hasRole('operator');
  }

  /**
   * Check if user can only view
   */
  isViewer(): boolean {
    return this.hasRole('viewer');
  }

  /**
   * Get authorization header value
   */
  getAuthHeader(): string | null {
    if (!this.authState.token) {
      return null;
    }
    return `Bearer ${this.authState.token}`;
  }

  /**
   * Refresh token (for future implementation)
   */
  async refreshToken(): Promise<void> {
    // TODO: Implement token refresh when backend supports it

  }

  /**
   * Handle authentication errors
   */
  handleAuthError(error: any): void {
    if (error.response?.status === 401) {

      this.logout();
    }
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();


