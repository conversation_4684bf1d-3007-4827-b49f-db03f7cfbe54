{"name": "@rpa-project/frontend", "version": "1.0.0", "description": "React frontend with React Flow for RPA visual designer", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@monaco-editor/react": "^4.7.0", "@rpa-project/shared": "file:../shared", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "reactflow": "^11.10.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.2.2", "vite": "^7.0.5"}}