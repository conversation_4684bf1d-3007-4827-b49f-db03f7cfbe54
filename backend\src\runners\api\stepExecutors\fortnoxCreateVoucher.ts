import axios from 'axios';
import type { FortnoxCreateVoucherStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';
import { LLMService } from '../../../services/llm/LLMService';
import { LLMProviderFactory } from '../../../services/llm/LLMProviderFactory';

/**
 * Executor context for Fortnox steps
 */
export interface FortnoxExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string; data?: any }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
  // openai?: OpenAI; // TA BORT denna rad
}

/**
 * Fortnox API account interface
 */
interface FortnoxAccount {
  Number: string;
  Description: string;
  Active: boolean;
  BalanceBroughtForward?: number;
  BalanceCarriedForward?: number;
  SRU?: number;
  Year?: number;
}

/**
 * Fortnox API chart of accounts response
 */
interface FortnoxAccountsResponse {
  Accounts: FortnoxAccount[];
}

/**
 * Fortnox voucher row interface
 */
interface FortnoxVoucherRow {
  Account: string;
  Debit?: number;
  Credit?: number;
  Description?: string;
}

/**
 * Fortnox voucher interface
 */
interface FortnoxVoucher {
  Description?: string;
  TransactionDate?: string;
  VoucherSeries?: string;
  VoucherRows: FortnoxVoucherRow[];
}

/**
 * AI response interface for voucher rows
 */
interface AIVoucherRowsResponse {
  rows: Array<{
    account: string;
    debit?: number;
    credit?: number;
    description?: string;
  }>;
  transactionDate?: string; // AI can specify transaction date
  explanation?: string;
}

/**
 * Execute Fortnox Create Voucher step
 */
export async function executeFortnoxCreateVoucher(
  step: FortnoxCreateVoucherStep,
  context: FortnoxExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing Fortnox Create Voucher: ${step.id}`,
      stepId: step.id
    });

    // Get Fortnox token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for Fortnox API calls');
    }

    const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
    const fortnoxToken = fortnoxTokens.find(token => token.provider === 'Fortnox' && token.apiToken);

    if (!fortnoxToken || !fortnoxToken.apiToken) {
      throw new Error('No valid Fortnox token found for customer');
    }

    onLog({
      level: 'info',
      message: 'Found Fortnox token, proceeding with voucher creation...',
      stepId: step.id
    });

    // Get input data from variable
    // Handle both direct variable names and ${variableName} syntax
    let inputData;
    let actualVariableName = step.inputVariable;

    // Check if inputVariable contains ${...} syntax and extract the variable name
    const variableMatch = step.inputVariable.match(/^\$\{([^}]+)\}$/);
    if (variableMatch) {
      actualVariableName = variableMatch[1];
    }

    inputData = variables[actualVariableName];
    if (inputData === undefined || inputData === null) {
      onLog({
        level: 'error',
        message: `Available variables: ${Object.keys(variables).join(', ')}`,
        stepId: step.id
      });
      throw new Error(`Input variable '${actualVariableName}' not found or is null`);
    }

    // Get provider and model info for logging
    const provider = LLMProviderFactory.getInstance();
    const defaultModel = LLMProviderFactory.getDefaultModel();

    onLog({
      level: 'info',
      message: `Sending data to ${provider.name} (${defaultModel}) for voucher row generation...`,
      stepId: step.id
    });

    // Create system prompt for AI
    const systemPrompt = `Du är en expert på svensk bokföring och Fortnox-verifikationer. Din uppgift är att skapa korrekta verifikationsrader baserat på input-data.

VIKTIGA REGLER:
1. Verifikationen MÅSTE vara balanserad (totalt debet = totalt kredit)
2. Använd endast giltiga kontonummer enligt svensk kontoplan (BAS-kontoplanen)
3. Alla belopp måste vara positiva tal
4. Om input-data innehåller ett datum, använd det som transactionDate
5. Svara ENDAST med JSON i följande format:

{
  "rows": [
    {
      "account": "kontonummer",
      "debit": belopp_eller_null,
      "credit": belopp_eller_null,
      "description": "beskrivning"
    }
  ],
  "transactionDate": "YYYY-MM-DD (om datum finns i input-data)",
  "explanation": "kort förklaring av verifikationen"
}

INSTRUKTIONER:
Användaren kommer att ange vilka konton som ska användas i sin prompt. Följ användarens instruktioner för kontohantering.`;

    // Interpolate the AI prompt with variables
    const interpolatedPrompt = interpolateVariables(step.aiPrompt, variables);

    // Prepare input data as string
    const inputDataStr = typeof inputData === 'string' ? inputData : JSON.stringify(inputData, null, 2);

    // Send to LLM for processing
    const completion = await LLMService.createChatCompletion([
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: `Skapa verifikationsrader för följande data:

INPUT DATA:
${inputDataStr}

INSTRUKTIONER:
${interpolatedPrompt}

Skapa en korrekt balanserad verifikation enligt svensk bokföringssed.`
      }
    ], {
      temperature: 0.1, // Lower temperature for more consistent output
      maxTokens: 2000
    });

    const aiResponse = completion.content;
    if (!aiResponse) {
      throw new Error('No response from LLM');
    }

    onLog({
      level: 'info',
      message: `${provider.name} response received, parsing voucher rows...`,
      stepId: step.id
    });

    // Parse AI response
    let aiVoucherData: AIVoucherRowsResponse;
    try {
      // Clean the response to extract JSON
      let jsonStr = aiResponse.trim();

      // Remove markdown code blocks if present
      if (jsonStr.startsWith('```json')) {
        jsonStr = jsonStr.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (jsonStr.startsWith('```')) {
        jsonStr = jsonStr.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      aiVoucherData = JSON.parse(jsonStr);

      if (!aiVoucherData.rows || !Array.isArray(aiVoucherData.rows)) {
        throw new Error('AI response must contain a "rows" array');
      }

    } catch (parseError) {
      const errorMessage = parseError instanceof Error ? parseError.message : 'Unknown JSON parsing error';
      onLog({
        level: 'error',
        message: `Failed to parse AI response as JSON: ${errorMessage}. Raw response: ${aiResponse.substring(0, 200)}...`,
        stepId: step.id
      });
      throw new Error(`Invalid JSON response from AI: ${errorMessage}`);
    }

    // Validate and convert AI response to Fortnox format
    const voucherRows: FortnoxVoucherRow[] = [];
    let totalDebit = 0;
    let totalCredit = 0;

    for (const aiRow of aiVoucherData.rows) {
      // Validate amounts
      if (aiRow.debit && aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} cannot have both debit and credit`);
      }

      if (!aiRow.debit && !aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} must have either debit or credit`);
      }

      // Validate account number format (basic validation)
      if (!aiRow.account || !/^\d+$/.test(aiRow.account)) {
        throw new Error(`Invalid account number format: ${aiRow.account}. Must be numeric.`);
      }

      const voucherRow: FortnoxVoucherRow = {
        Account: aiRow.account,
        Description: aiRow.description || interpolateVariables(step.description || '', variables)
      };

      if (aiRow.debit) {
        const debitAmount = parseFloat(aiRow.debit.toString());
        if (isNaN(debitAmount) || debitAmount <= 0) {
          throw new Error(`Invalid debit amount for account ${aiRow.account}: ${aiRow.debit}`);
        }
        voucherRow.Debit = debitAmount;
        totalDebit += debitAmount;
      }

      if (aiRow.credit) {
        const creditAmount = parseFloat(aiRow.credit.toString());
        if (isNaN(creditAmount) || creditAmount <= 0) {
          throw new Error(`Invalid credit amount for account ${aiRow.account}: ${aiRow.credit}`);
        }
        voucherRow.Credit = creditAmount;
        totalCredit += creditAmount;
      }

      voucherRows.push(voucherRow);

      onLog({
        level: 'info',
        message: `Added row: Account ${aiRow.account} - ${aiRow.debit ? `Debit: ${aiRow.debit}` : `Credit: ${aiRow.credit}`}`,
        stepId: step.id
      });
    }

    // Validate that debits equal credits
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      throw new Error(`Voucher is not balanced: Total debit (${totalDebit}) != Total credit (${totalCredit})`);
    }

    onLog({
      level: 'info',
      message: `AI generated ${voucherRows.length} balanced voucher rows. ${aiVoucherData.explanation || ''}`,
      stepId: step.id
    });

    // Determine transaction date - prioritize AI response, then step config, then today
    let transactionDate = step.transactionDate;
    if (aiVoucherData.transactionDate) {
      // Validate AI-provided date format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(aiVoucherData.transactionDate)) {
        transactionDate = aiVoucherData.transactionDate;
        onLog({
          level: 'info',
          message: `Using AI-provided transaction date: ${aiVoucherData.transactionDate}`,
          stepId: step.id
        });
      } else {
        onLog({
          level: 'warn',
          message: `AI provided invalid date format: ${aiVoucherData.transactionDate}. Using fallback.`,
          stepId: step.id
        });
      }
    }

    // Fallback to today if no valid date is available
    if (!transactionDate) {
      transactionDate = new Date().toISOString().split('T')[0];
    }

    // Create voucher
    const voucher: FortnoxVoucher = {
      Description: interpolateVariables(step.description || 'AI Generated Voucher', variables),
      TransactionDate: transactionDate,
      VoucherSeries: step.voucherSeries || 'A',
      VoucherRows: voucherRows
    };

    onLog({
      level: 'info',
      message: `Creating voucher with ${voucherRows.length} rows (Total: ${totalDebit})`,
      stepId: step.id
    });

    // Send voucher to Fortnox
    let voucherResponse;
    try {
      voucherResponse = await axios.post(
        'https://api.fortnox.se/3/vouchers',
        { Voucher: voucher },
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
    } catch (apiError: any) {
      // Log detailed error information from Fortnox API
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      // Also log the voucher data that was sent
      onLog({
        level: 'error',
        message: `Voucher data sent to Fortnox: ${JSON.stringify({ Voucher: voucher }, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const createdVoucher = voucherResponse.data.Voucher;

    // Attach files if fileIds are provided
    const attachedFiles: string[] = [];
    if (step.fileIds && step.fileIds.length > 0) {
      onLog({
        level: 'info',
        message: `Attaching ${step.fileIds.length} files to voucher ${createdVoucher.VoucherSeries}${createdVoucher.VoucherNumber}`,
        stepId: step.id
      });

      for (const fileId of step.fileIds) {
        try {
          const interpolatedFileId = interpolateVariables(fileId, variables);

          // Extract file ID if it's an object with fileId property
          let actualFileId: string;
          const fileData = variables[interpolatedFileId];
          if (typeof fileData === 'string') {
            actualFileId = fileData;
          } else if (fileData && typeof fileData === 'object' && fileData.fileId) {
            actualFileId = fileData.fileId;
          } else {
            actualFileId = interpolatedFileId; // Assume it's a direct file ID
          }

          // Create voucher file connection
          const voucherFileConnection = {
            FileId: actualFileId,
            VoucherNumber: createdVoucher.VoucherNumber,
            VoucherSeries: createdVoucher.VoucherSeries
          };

          await axios.post(
            'https://api.fortnox.se/3/voucherfileconnections',
            { VoucherFileConnection: voucherFileConnection },
            {
              headers: {
                'Authorization': `Bearer ${fortnoxToken.apiToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              }
            }
          );

          attachedFiles.push(actualFileId);
          onLog({
            level: 'info',
            message: `File ${actualFileId} attached to voucher successfully`,
            stepId: step.id
          });

        } catch (attachError: any) {
          onLog({
            level: 'warn',
            message: `Failed to attach file ${fileId}: ${attachError.message}`,
            stepId: step.id
          });
        }
      }
    }

    // Store voucher information in variables
    const variableName = step.variableName || getDefaultVariableName('fortnoxCreateVoucher', stepIndex);
    const voucherResult = {
      voucherNumber: createdVoucher.VoucherNumber,
      voucherSeries: createdVoucher.VoucherSeries,
      voucherId: createdVoucher.VoucherNumber,
      totalAmount: totalDebit,
      rowsCount: voucherRows.length,
      aiExplanation: aiVoucherData.explanation,
      attachedFiles: attachedFiles,
      fullResponse: createdVoucher
    };

    variables[variableName] = voucherResult;

    onLog({
      level: 'info',
      message: `Voucher created successfully: ${createdVoucher.VoucherSeries}${createdVoucher.VoucherNumber}${attachedFiles.length > 0 ? ` with ${attachedFiles.length} attached files` : ''}`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: voucherResult
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Error creating Fortnox voucher: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
