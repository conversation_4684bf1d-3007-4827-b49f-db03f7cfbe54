import bcrypt from 'bcrypt';
import crypto from 'crypto';
import { generateId } from '@rpa-project/shared';
import {
  User,
  Api<PERSON>ey,
  UserRole
} from '../../types/permissions';

// Define request/response types locally
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role: User<PERSON>ole;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  role?: UserRole;
  isActive?: boolean;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'password'>;
  token: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface CreateApiKeyRequest {
  name: string;
  description?: string;
  role: UserRole;
  expiresAt?: Date;
}

export interface UpdateApiKeyRequest {
  name?: string;
  description?: string;
  role?: UserRole;
  isActive?: boolean;
  expiresAt?: Date;
}

export interface ApiKeyResponse {
  apiKey: Omit<ApiKey, 'keyHash'>;
  key: string; // Only returned on creation
}
import { jwtService, TokenPair } from './jwtService';
import { securityConfig, validatePassword, isCommonWeakPassword, apiKeyConfig } from '../../config/security';
import { securityLogService } from './securityLogService';

import { statements } from '../../database/database';
/**
 * Authentication Service
 */
export class AuthService {
  private apiKeys = new Map<string, ApiKey>();
  private loginAttempts = new Map<string, { count: number; lastAttempt: Date; lockedUntil?: Date }>();

  constructor() {
    // Users are stored in SQLite; default admin is seeded via DB migration.
  }

  // Default admin is created during DB migration; no-op here
  private async initializeDefaultUser(): Promise<void> {
    return;
  }

  /**
   * Register new user
   */
  async registerUser(request: CreateUserRequest): Promise<User> {
    // Validate password
    const passwordErrors = validatePassword(request.password, {
      username: request.username,
      email: request.email,
    });

    if (passwordErrors.length > 0) {
      throw new Error(`Password validation failed: ${passwordErrors.join(', ')}`);
    }

    if (isCommonWeakPassword(request.password)) {
      throw new Error('Password is too common and easily guessable');
    }

    // Check if username or email already exists in DB
    const exists = statements.userExistsByUsernameOrEmail.get(request.username, request.email);
    if (exists) {
      throw new Error('Username or email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(request.password, securityConfig.security.bcryptRounds);

    // Create user
    const user: User = {
      id: generateId(),
      username: request.username,
      email: request.email,
      role: request.role,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Persist user
    statements.insertUser.run(
      user.id,
      user.username,
      user.email,
      hashedPassword,
      user.role === 'admin' ? 'admin' : 'user',
      user.isActive ? 1 : 0,
      user.createdAt.toISOString(),
      user.updatedAt.toISOString()
    );

    await securityLogService.log({
      type: 'user_created',
      userId: user.id,
      ip: '127.0.0.1', // This should be passed from the request
      endpoint: '/api/auth/register',
      method: 'POST',
      statusCode: 201,
      message: `User ${user.username} created with role ${user.role}`,
    });

    return user;
  }

  /**
   * Authenticate user login
   */
  async login(request: LoginRequest, ip: string): Promise<LoginResponse> {
    const identifier = request.username.toLowerCase();

    // Check for account lockout
    const attempts = this.loginAttempts.get(identifier);
    if (attempts?.lockedUntil && attempts.lockedUntil > new Date()) {
      await securityLogService.log({
        type: 'login_failed',
        ip,
        endpoint: '/api/auth/login',
        method: 'POST',
        statusCode: 423,
        message: `Login attempt on locked account: ${request.username}`,
      });
      throw new Error('Account is temporarily locked due to too many failed login attempts');
    }

    // Find user in DB
    const row = statements.getUserByUsernameOrEmail.get(identifier, identifier) as any;
    if (!row || row.is_active !== 1) {
      await this.recordFailedLogin(identifier, ip);
      throw new Error('Invalid username or password');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(request.password, row.password_hash);
    if (!isValidPassword) {
      await this.recordFailedLogin(identifier, ip);
      throw new Error('Invalid username or password');
    }

    // Reset login attempts on successful login
    this.loginAttempts.delete(identifier);

    // Update last login
    const nowIso = new Date().toISOString();
    statements.updateUserLastLogin.run(nowIso, nowIso, row.id);

    const user: User = {
      id: row.id,
      username: row.username,
      email: row.email,
      role: row.role as any,
      isActive: row.is_active === 1,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(nowIso),
      lastLoginAt: new Date(nowIso)
    };

    // Generate tokens
    const tokenPair = jwtService.generateTokenPair(user);

    await securityLogService.log({
      type: 'login_success',
      userId: user.id,
      ip,
      endpoint: '/api/auth/login',
      method: 'POST',
      statusCode: 200,
      message: `User ${user.username} logged in successfully`,
    });

    return {
      user,
      token: tokenPair.accessToken,
      refreshToken: tokenPair.refreshToken,
      expiresAt: tokenPair.expiresAt,
    };
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string, ip: string): Promise<TokenPair | null> {
    const tokenPair = await jwtService.refreshAccessToken(refreshToken, (id) => this.getUserById(id));

    if (tokenPair) {
      await securityLogService.log({
        type: 'token_refresh',
        ip,
        endpoint: '/api/auth/refresh',
        method: 'POST',
        statusCode: 200,
        message: 'Access token refreshed successfully',
      });
    }

    return tokenPair;
  }

  /**
   * Logout user
   */
  async logout(refreshToken: string, userId: string, ip: string): Promise<void> {
    jwtService.revokeRefreshToken(refreshToken);

    await securityLogService.log({
      type: 'logout',
      userId,
      ip,
      endpoint: '/api/auth/logout',
      method: 'POST',
      statusCode: 200,
      message: 'User logged out successfully',
    });
  }

  /**
   * Change user password
   */
  async changePassword(userId: string, request: ChangePasswordRequest, ip: string): Promise<void> {
    const row = statements.getUserByIdRecord.get(userId) as any;
    if (!row) {
      throw new Error('User not found');
    }

    // Verify current password
    const isValidCurrentPassword = await bcrypt.compare(request.currentPassword, row.password_hash);
    if (!isValidCurrentPassword) {
      throw new Error('Current password is incorrect');
    }

    // Validate new password
    const passwordErrors = validatePassword(request.newPassword, {
      username: row.username,
      email: row.email,
    });

    if (passwordErrors.length > 0) {
      throw new Error(`Password validation failed: ${passwordErrors.join(', ')}`);
    }

    if (isCommonWeakPassword(request.newPassword)) {
      throw new Error('Password is too common and easily guessable');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(request.newPassword, securityConfig.security.bcryptRounds);

    // Update password in DB
    const nowIso = new Date().toISOString();
    statements.updateUserPassword.run(hashedPassword, nowIso, userId);

    // Revoke all existing tokens
    jwtService.revokeAllUserTokens(userId);

    await securityLogService.log({
      type: 'password_changed',
      userId,
      ip,
      endpoint: '/api/auth/change-password',
      method: 'POST',
      statusCode: 200,
      message: 'Password changed successfully',
    });
  }

  /**
   * Create API key
   */
  async createApiKey(request: CreateApiKeyRequest): Promise<ApiKeyResponse> {
    // Generate API key
    const keyPrefix = apiKeyConfig.prefixes.api;
    const keyBody = jwtService.generateSecureRandomString(apiKeyConfig.keyLength - keyPrefix.length);
    const apiKeyValue = keyPrefix + keyBody;

    // Hash the key for storage
    const keyHash = jwtService.hashApiKey(apiKeyValue);

    // Create API key record
    const apiKey: ApiKey = {
      id: generateId(),
      name: request.name,
      description: request.description,
      keyHash,
      role: request.role,
      isActive: true,
      expiresAt: request.expiresAt,
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0,
    };

    this.apiKeys.set(apiKey.id, apiKey);

    await securityLogService.log({
      type: 'api_key_created',
      ip: '127.0.0.1', // This should be passed from the request
      endpoint: '/api/auth/api-keys',
      method: 'POST',
      statusCode: 201,
      message: `API key ${apiKey.name} created with role ${apiKey.role}`,
      metadata: { apiKeyId: apiKey.id },
    });

    return {
      apiKey: { ...apiKey, keyHash: undefined } as any,
      key: apiKeyValue,
    };
  }

  /**
   * Verify API key
   */
  async verifyApiKey(apiKeyValue: string): Promise<ApiKey | null> {
    const keyHash = jwtService.hashApiKey(apiKeyValue);

    for (const apiKey of this.apiKeys.values()) {
      if (apiKey.isActive && jwtService.verifyApiKeyHash(apiKeyValue, apiKey.keyHash)) {
        // Check expiration
        if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
          return null;
        }

        // Update usage statistics
        apiKey.usageCount++;
        apiKey.lastUsedAt = new Date();
        this.apiKeys.set(apiKey.id, apiKey);

        return apiKey;
      }
    }

    return null;
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<User | null> {
    const row = statements.getUserByIdRecord.get(id) as any;
    if (!row) return null;
    return {
      id: row.id,
      username: row.username,
      email: row.email,
      role: row.role,
      isActive: row.is_active === 1,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      lastLoginAt: row.last_login_at ? new Date(row.last_login_at) : undefined,
    };
  }

  /**
   * Get all users
   */
  async getAllUsers(): Promise<User[]> {
    const rows = statements.getAllUsersRecords.all() as any[];
    return rows.map(row => ({
      id: row.id,
      username: row.username,
      email: row.email,
      role: row.role,
      isActive: row.isActive === 1 || row.isActive === true,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      lastLoginAt: row.lastLoginAt ? new Date(row.lastLoginAt) : undefined,
    }));
  }

  /**
   * Update user
   */
  async updateUser(id: string, request: UpdateUserRequest): Promise<User> {
    const row = statements.getUserByIdRecord.get(id) as any;
    if (!row) {
      throw new Error('User not found');
    }

    const username = request.username ?? row.username;
    const email = request.email ?? row.email;
    const role = (request.role ?? row.role) as any;
    const isActive = request.isActive ?? (row.is_active === 1);
    const nowIso = new Date().toISOString();

    statements.updateUserRecord.run(
      username,
      email,
      role,
      isActive ? 1 : 0,
      nowIso,
      id
    );

    await securityLogService.log({
      type: 'user_updated',
      userId: id,
      ip: '127.0.0.1', // This should be passed from the request
      endpoint: `/api/auth/users/${id}`,
      method: 'PUT',
      statusCode: 200,
      message: `User ${username} updated`,
    });

    return {
      id,
      username,
      email,
      role,
      isActive,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(nowIso),
      lastLoginAt: row.last_login_at ? new Date(row.last_login_at) : undefined,
    };
  }

  /**
   * Record failed login attempt
   */
  private async recordFailedLogin(identifier: string, ip: string): Promise<void> {
    const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: new Date() };
    attempts.count++;
    attempts.lastAttempt = new Date();

    // Lock account if too many attempts
    if (attempts.count >= securityConfig.security.maxLoginAttempts) {
      attempts.lockedUntil = new Date(Date.now() + securityConfig.security.lockoutDuration * 60 * 1000);
    }

    this.loginAttempts.set(identifier, attempts);

    await securityLogService.log({
      type: 'login_failed',
      ip,
      endpoint: '/api/auth/login',
      method: 'POST',
      statusCode: 401,
      message: `Failed login attempt for: ${identifier} (attempt ${attempts.count})`,
    });
  }
}

// Export singleton instance
export const authService = new AuthService();
