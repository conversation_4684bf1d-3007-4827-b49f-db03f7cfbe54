import { Router, Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import { ApiResponse } from '@rpa-project/shared';
import { PERMISSIONS, User, ApiKey } from '../types/permissions';
import {
  LoginRequest,
  LoginResponse,
  CreateUserRequest,
  UpdateUserRequest,
  CreateApiKeyRequest,
  UpdateApiKeyRequest,
  ChangePasswordRequest
} from '../services/auth/authService';
import { authService } from '../services/auth/authService';
import { securityLogService } from '../services/auth/securityLogService';
import { securityMonitoringService } from '../services/auth/securityMonitoringService';
import { jwtService } from '../services/auth/jwtService';
import { asyncHandler } from '../middleware/errorHandler';
import { 
  authenticate, 
  authorize, 
  requireAdmin, 
  requireAuth,
  requireActiveUser 
} from '../middleware/auth';
import { authRateLimit, strictRateLimit } from '../middleware/rateLimiter';

const router = Router();

// Validation schemas
const loginSchema = Joi.object({
  username: Joi.string().required().min(1).max(100),
  password: Joi.string().required().min(1),
});

interface RefreshTokenRequest {
  refreshToken: string;
}

const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required(),
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().required().min(8).max(128),
});

const createUserSchema = Joi.object({
  username: Joi.string().required().min(1).max(50),
  email: Joi.string().email().required().max(100),
  password: Joi.string().required().min(8).max(128),
  role: Joi.string().valid('admin', 'user').required(),
});

const updateUserSchema = Joi.object({
  username: Joi.string().optional().min(1).max(50),
  email: Joi.string().email().optional().max(100),
  role: Joi.string().valid('admin', 'user').optional(),
  isActive: Joi.boolean().optional(),
});

const createApiKeySchema = Joi.object({
  name: Joi.string().required().min(1).max(100),
  description: Joi.string().optional().max(500),
  role: Joi.string().valid('admin', 'operator', 'viewer', 'api').required(),
  expiresAt: Joi.date().optional().greater('now'),
});

const updateApiKeySchema = Joi.object({
  name: Joi.string().optional().min(1).max(100),
  description: Joi.string().optional().max(500),
  role: Joi.string().valid('admin', 'operator', 'viewer', 'api').optional(),
  isActive: Joi.boolean().optional(),
  expiresAt: Joi.date().optional().greater('now'),
});

// Helper function to get client IP
function getClientIP(req: Request): string {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    (req.headers['x-real-ip'] as string) ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    '127.0.0.1'
  );
}

// POST /api/auth/login - User login
router.post('/login', authRateLimit, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = loginSchema.validate(req.body);
  if (error) {
    const response: ApiResponse = {
      success: false,
      error: error.details[0].message,
    };
    return res.status(400).json(response);
  }

  const loginRequest: LoginRequest = value;
  const ip = getClientIP(req);

  try {
    const loginResponse = await authService.login(loginRequest, ip);

    const response: ApiResponse<LoginResponse> = {
      success: true,
      data: loginResponse,
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Login failed',
    };
    res.status(401).json(response);
  }
}));

// POST /api/auth/refresh - Refresh access token
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = refreshTokenSchema.validate(req.body);
  if (error) {
    const response: ApiResponse = {
      success: false,
      error: error.details[0].message,
    };
    return res.status(400).json(response);
  }

  const { refreshToken } = value;
  const ip = getClientIP(req);

  try {
    const tokenPair = await authService.refreshToken(refreshToken, ip);
    if (!tokenPair) {
      throw new Error('Invalid or expired refresh token');
    }

    const response: ApiResponse = {
      success: true,
      data: {
        token: tokenPair.accessToken,
        refreshToken: tokenPair.refreshToken,
        expiresAt: tokenPair.expiresAt,
      },
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Token refresh failed',
    };
    res.status(401).json(response);
  }
}));

// POST /api/auth/logout - User logout
router.post('/logout', requireAuth, asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;
  const userId = req.auth?.user?.id;
  const ip = getClientIP(req);

  if (refreshToken && userId) {
    await authService.logout(refreshToken, userId, ip);
  }

  const response: ApiResponse = {
    success: true,
    data: { message: 'Logged out successfully' },
  };

  res.json(response);
}));

// POST /api/auth/change-password - Change user password
router.post('/change-password', requireAuth, requireActiveUser, strictRateLimit, 
  asyncHandler(async (req: Request, res: Response) => {
    const { error, value } = changePasswordSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message,
      };
      return res.status(400).json(response);
    }

    const changePasswordRequest: ChangePasswordRequest = value;
    const userId = req.auth!.user!.id;
    const ip = getClientIP(req);

    try {
      await authService.changePassword(userId, changePasswordRequest, ip);

      const response: ApiResponse = {
        success: true,
        data: { message: 'Password changed successfully' },
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Password change failed',
      };
      res.status(400).json(response);
    }
  })
);

// GET /api/auth/me - Get current user info
router.get('/me', requireAuth, asyncHandler(async (req: Request, res: Response) => {
  const response: ApiResponse<User> = {
    success: true,
    data: req.auth!.user!,
  };

  res.json(response);
}));

// GET /api/auth/users - Get all users (admin only)
router.get('/users', requireAuth, authorize(PERMISSIONS.USERS_READ), 
  asyncHandler(async (req: Request, res: Response) => {
    const users = await authService.getAllUsers();

    const response: ApiResponse<User[]> = {
      success: true,
      data: users,
    };

    res.json(response);
  })
);

// POST /api/auth/users - Create new user (admin only)
router.post('/users', requireAuth, authorize(PERMISSIONS.USERS_CREATE), 
  asyncHandler(async (req: Request, res: Response) => {
    const { error, value } = createUserSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message,
      };
      return res.status(400).json(response);
    }

    const createUserRequest: CreateUserRequest = value;

    try {
      const user = await authService.registerUser(createUserRequest);

      const response: ApiResponse<User> = {
        success: true,
        data: user,
      };

      res.status(201).json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'User creation failed',
      };
      res.status(400).json(response);
    }
  })
);

// PUT /api/auth/users/:id - Update user (admin only)
router.put('/users/:id', requireAuth, authorize(PERMISSIONS.USERS_UPDATE), 
  asyncHandler(async (req: Request, res: Response) => {
    const { error, value } = updateUserSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message,
      };
      return res.status(400).json(response);
    }

    const updateUserRequest: UpdateUserRequest = value;
    const userId = req.params.id;

    try {
      const user = await authService.updateUser(userId, updateUserRequest);

      const response: ApiResponse<User> = {
        success: true,
        data: user,
      };

      res.json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'User update failed',
      };
      res.status(400).json(response);
    }
  })
);

// POST /api/auth/api-keys - Create API key (admin only)
router.post('/api-keys', requireAuth, authorize(PERMISSIONS.API_KEYS_CREATE), 
  asyncHandler(async (req: Request, res: Response) => {
    const { error, value } = createApiKeySchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message,
      };
      return res.status(400).json(response);
    }

    const createApiKeyRequest: CreateApiKeyRequest = value;

    try {
      const apiKeyResponse = await authService.createApiKey(createApiKeyRequest);

      const response: ApiResponse = {
        success: true,
        data: apiKeyResponse,
      };

      res.status(201).json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'API key creation failed',
      };
      res.status(400).json(response);
    }
  })
);

// GET /api/auth/security/logs - Get security logs (admin only)
router.get('/security/logs', requireAuth, authorize(PERMISSIONS.SECURITY_LOGS_READ), 
  asyncHandler(async (req: Request, res: Response) => {
    const { type, userId, ip, startDate, endDate, limit = 50, offset = 0 } = req.query;

    const options = {
      type: type as any,
      userId: userId as string,
      ip: ip as string,
      startDate: startDate ? new Date(startDate as string) : undefined,
      endDate: endDate ? new Date(endDate as string) : undefined,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
    };

    const result = await securityLogService.getLogs(options);

    const response: ApiResponse = {
      success: true,
      data: result,
    };

    res.json(response);
  })
);

// GET /api/auth/security/statistics - Get security statistics (admin only)
router.get('/security/statistics', requireAuth, authorize(PERMISSIONS.SECURITY_LOGS_READ), 
  asyncHandler(async (req: Request, res: Response) => {
    const { timeRange = 'day' } = req.query;
    const statistics = await securityLogService.getStatistics(timeRange as any);

    const response: ApiResponse = {
      success: true,
      data: statistics,
    };

    res.json(response);
  })
);

// GET /api/auth/security/suspicious - Get suspicious activity (admin only)
router.get('/security/suspicious', requireAuth, authorize(PERMISSIONS.SECURITY_LOGS_READ),
  asyncHandler(async (req: Request, res: Response) => {
    const suspiciousActivity = await securityLogService.detectSuspiciousActivity();

    const response: ApiResponse = {
      success: true,
      data: suspiciousActivity,
    };

    res.json(response);
  })
);

// GET /api/auth/security/dashboard - Get security dashboard data (admin only)
router.get('/security/dashboard', requireAuth, authorize(PERMISSIONS.SECURITY_LOGS_READ),
  asyncHandler(async (req: Request, res: Response) => {
    const dashboardData = await securityMonitoringService.getDashboardData();

    const response: ApiResponse = {
      success: true,
      data: dashboardData,
    };

    res.json(response);
  })
);

// GET /api/auth/security/alerts - Get security alerts (admin only)
router.get('/security/alerts', requireAuth, authorize(PERMISSIONS.SECURITY_LOGS_READ),
  asyncHandler(async (req: Request, res: Response) => {
    const { type } = req.query;
    const alerts = securityMonitoringService.getAlerts(type as any);

    const response: ApiResponse = {
      success: true,
      data: alerts,
    };

    res.json(response);
  })
);

// POST /api/auth/security/alerts/:id/acknowledge - Acknowledge security alert (admin only)
router.post('/security/alerts/:id/acknowledge', requireAuth, authorize(PERMISSIONS.SECURITY_LOGS_READ),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const acknowledged = securityMonitoringService.acknowledgeAlert(id);

    if (!acknowledged) {
      const response: ApiResponse = {
        success: false,
        error: 'Alert not found',
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: { message: 'Alert acknowledged successfully' },
    };

    res.json(response);
  })
);

// GET /api/auth/security/health - Get security health score (admin only)
router.get('/security/health', requireAuth, authorize(PERMISSIONS.SECURITY_LOGS_READ),
  asyncHandler(async (req: Request, res: Response) => {
    const healthScore = securityMonitoringService.getSecurityHealthScore();
    const metrics = securityMonitoringService.getMetrics();

    const response: ApiResponse = {
      success: true,
      data: {
        healthScore,
        metrics,
        status: healthScore >= 80 ? 'good' : healthScore >= 60 ? 'warning' : 'critical',
      },
    };

    res.json(response);
  })
);

export { router as authRoutes };
