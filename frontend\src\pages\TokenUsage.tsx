import { useState, useEffect } from 'react';

interface TokenUsageLog {
  timestamp: string;
  operation: string;
  model: string;
  provider: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  stepId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

interface UsageStats {
  totalTokens: number;
  promptTokens: number;
  completionTokens: number;
  operationCount: number;
}

interface DashboardData {
  period: string;
  sinceDate: string;
  totalStats: UsageStats;
  operationSummary: Record<string, UsageStats>;
  modelSummary: Record<string, UsageStats>;
  recentLogs: TokenUsageLog[];
}

export default function TokenUsage() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('24h');
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/token-usage/dashboard?since=${period}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      if (result.success) {
        setDashboardData(result.data);
      } else {
        throw new Error(result.message || 'Failed to fetch token usage data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      console.error('Error fetching token usage data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, [period]);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('sv-SE');
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar token-användning...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-container">
        <div className="error-card">
          <div className="error-content">
            <div className="error-icon">⚠️</div>
            <div>
              <h3 className="error-title">Fel vid hämtning av data</h3>
              <p className="error-message">{error}</p>
            </div>
          </div>
          <button
            onClick={fetchDashboardData}
            className="btn btn-outline"
            style={{ marginTop: '1rem' }}
          >
            🔄 Försök igen
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="dashboard-container">
        <div className="card">
          <p>Ingen data tillgänglig</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Token-användning</p>
          <p className="dashboard-subtitle">
            Övervaka LLM token-förbrukning och kostnader för dina automatiseringar.
          </p>
        </div>
        <div className="dashboard-actions">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="form-select"
            style={{ marginRight: '1rem' }}
          >
            <option value="24h">24 timmar</option>
            <option value="7d">7 dagar</option>
            <option value="30d">30 dagar</option>
          </select>
          <button
            onClick={fetchDashboardData}
            className="btn btn-outline"
          >
            🔄 Uppdatera
          </button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-header">
            <div className="stat-title">Totala Tokens</div>
            <div className="stat-icon">⚡</div>
          </div>
          <div className="stat-value">{formatNumber(dashboardData.totalStats.totalTokens)}</div>
        </div>

        <div className="stat-card">
          <div className="stat-header">
            <div className="stat-title">Prompt Tokens</div>
            <div className="stat-icon">📝</div>
          </div>
          <div className="stat-value">{formatNumber(dashboardData.totalStats.promptTokens)}</div>
        </div>

        <div className="stat-card">
          <div className="stat-header">
            <div className="stat-title">Completion Tokens</div>
            <div className="stat-icon">📊</div>
          </div>
          <div className="stat-value">{formatNumber(dashboardData.totalStats.completionTokens)}</div>
        </div>

        <div className="stat-card">
          <div className="stat-header">
            <div className="stat-title">Antal Operationer</div>
            <div className="stat-icon">🔢</div>
          </div>
          <div className="stat-value">{formatNumber(dashboardData.totalStats.operationCount)}</div>
        </div>
      </div>

      {/* Operation Summary */}
      <div className="card-enhanced">
        <div className="card-enhanced-header">
          <h2 className="card-enhanced-title">Användning per Operation</h2>
        </div>
        <div className="card-enhanced-content">
          {Object.entries(dashboardData.operationSummary).length === 0 ? (
            <div className="empty-state">
              <p>Inga operationer att visa än</p>
            </div>
          ) : (
            <div className="operation-list">
              {Object.entries(dashboardData.operationSummary).map(([operation, stats]) => (
                <div key={operation} className="operation-item">
                  <div className="operation-info">
                    <span className="operation-badge">{operation}</span>
                    <span className="operation-count">
                      {stats.operationCount} operationer
                    </span>
                  </div>
                  <div className="operation-stats">
                    <div className="stat-main">{formatNumber(stats.totalTokens)} tokens</div>
                    <div className="stat-detail">
                      {formatNumber(stats.promptTokens)} prompt + {formatNumber(stats.completionTokens)} completion
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Model Summary */}
      <div className="card-enhanced">
        <div className="card-enhanced-header">
          <h2 className="card-enhanced-title">Användning per Modell</h2>
        </div>
        <div className="card-enhanced-content">
          {Object.entries(dashboardData.modelSummary).length === 0 ? (
            <div className="empty-state">
              <p>Inga modeller att visa än</p>
            </div>
          ) : (
            <div className="operation-list">
              {Object.entries(dashboardData.modelSummary).map(([model, stats]) => (
                <div key={model} className="operation-item">
                  <div className="operation-info">
                    <span className="model-badge">{model}</span>
                    <span className="operation-count">
                      {stats.operationCount} operationer
                    </span>
                  </div>
                  <div className="operation-stats">
                    <div className="stat-main">{formatNumber(stats.totalTokens)} tokens</div>
                    <div className="stat-detail">
                      {formatNumber(stats.promptTokens)} prompt + {formatNumber(stats.completionTokens)} completion
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Recent Logs */}
      <div className="card-enhanced">
        <div className="card-enhanced-header">
          <h2 className="card-enhanced-title">Senaste Operationer</h2>
        </div>
        <div className="card-enhanced-content">
          {dashboardData.recentLogs.length === 0 ? (
            <div className="empty-state">
              <p>Inga operationer att visa än</p>
            </div>
          ) : (
            <div className="logs-list">
              {dashboardData.recentLogs.map((log, index) => (
                <div key={index} className="log-item">
                  <div className="log-info">
                    <span className="operation-badge">{log.operation}</span>
                    <span className="log-model">{log.model}</span>
                    <span className="log-time">{formatDate(log.timestamp)}</span>
                  </div>
                  <div className="log-stats">
                    <div className="stat-main">{formatNumber(log.usage.totalTokens)} tokens</div>
                    <div className="stat-detail">
                      {formatNumber(log.usage.promptTokens)} + {formatNumber(log.usage.completionTokens)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
