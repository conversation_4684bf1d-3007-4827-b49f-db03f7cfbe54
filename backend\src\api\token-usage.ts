import { Router, Request, Response } from 'express';
import { TokenUsageLogger } from '../services/llm/TokenUsageLogger';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { ApiResponse } from '@rpa-project/shared';

const router = Router();

// GET /api/token-usage/stats - Get token usage statistics
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  const { 
    since, 
    operation, 
    model, 
    provider, 
    userId 
  } = req.query;

  // Parse since parameter
  let sinceDate: Date | undefined;
  if (since && typeof since === 'string') {
    sinceDate = new Date(since);
    if (isNaN(sinceDate.getTime())) {
      throw createError('Invalid since date format', 400);
    }
  }

  const stats = TokenUsageLogger.getUsageStats({
    since: sinceDate,
    operation: operation as string,
    model: model as string,
    provider: provider as string,
    userId: userId as string
  });

  const response: ApiResponse<typeof stats> = {
    success: true,
    data: stats,
    message: 'Token usage statistics retrieved successfully'
  };

  res.json(response);
}));

// GET /api/token-usage/summary/operations - Get usage summary by operation
router.get('/summary/operations', asyncHandler(async (req: Request, res: Response) => {
  const { since } = req.query;

  let sinceDate: Date | undefined;
  if (since && typeof since === 'string') {
    sinceDate = new Date(since);
    if (isNaN(sinceDate.getTime())) {
      throw createError('Invalid since date format', 400);
    }
  }

  const summary = TokenUsageLogger.getUsageSummaryByOperation(sinceDate);

  const response: ApiResponse<typeof summary> = {
    success: true,
    data: summary,
    message: 'Token usage summary by operation retrieved successfully'
  };

  res.json(response);
}));

// GET /api/token-usage/summary/models - Get usage summary by model
router.get('/summary/models', asyncHandler(async (req: Request, res: Response) => {
  const { since } = req.query;

  let sinceDate: Date | undefined;
  if (since && typeof since === 'string') {
    sinceDate = new Date(since);
    if (isNaN(sinceDate.getTime())) {
      throw createError('Invalid since date format', 400);
    }
  }

  const summary = TokenUsageLogger.getUsageSummaryByModel(sinceDate);

  const response: ApiResponse<typeof summary> = {
    success: true,
    data: summary,
    message: 'Token usage summary by model retrieved successfully'
  };

  res.json(response);
}));

// GET /api/token-usage/logs - Get recent token usage logs
router.get('/logs', asyncHandler(async (req: Request, res: Response) => {
  const { limit = '50' } = req.query;
  
  const limitNum = parseInt(limit as string, 10);
  if (isNaN(limitNum) || limitNum < 1 || limitNum > 1000) {
    throw createError('Limit must be a number between 1 and 1000', 400);
  }

  const allLogs = TokenUsageLogger.getAllLogs();
  const recentLogs = allLogs.slice(-limitNum).reverse(); // Get most recent first

  const response: ApiResponse<typeof recentLogs> = {
    success: true,
    data: recentLogs,
    message: `Retrieved ${recentLogs.length} recent token usage logs`
  };

  res.json(response);
}));

// GET /api/token-usage/dashboard - Get dashboard data with multiple summaries
router.get('/dashboard', asyncHandler(async (req: Request, res: Response) => {
  const { since = '24h' } = req.query;

  // Parse since parameter - support relative times like '24h', '7d', '30d'
  let sinceDate: Date;
  const now = new Date();
  
  if (since === '24h') {
    sinceDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  } else if (since === '7d') {
    sinceDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  } else if (since === '30d') {
    sinceDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  } else if (typeof since === 'string') {
    sinceDate = new Date(since);
    if (isNaN(sinceDate.getTime())) {
      throw createError('Invalid since parameter. Use "24h", "7d", "30d" or ISO date string', 400);
    }
  } else {
    sinceDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Default to 24h
  }

  const totalStats = TokenUsageLogger.getUsageStats({ since: sinceDate });
  const operationSummary = TokenUsageLogger.getUsageSummaryByOperation(sinceDate);
  const modelSummary = TokenUsageLogger.getUsageSummaryByModel(sinceDate);
  const recentLogs = TokenUsageLogger.getAllLogs().slice(-10).reverse();

  const dashboardData = {
    period: since,
    sinceDate,
    totalStats: {
      totalTokens: totalStats.totalTokens,
      promptTokens: totalStats.promptTokens,
      completionTokens: totalStats.completionTokens,
      operationCount: totalStats.operationCount
    },
    operationSummary,
    modelSummary,
    recentLogs
  };

  const response: ApiResponse<typeof dashboardData> = {
    success: true,
    data: dashboardData,
    message: 'Token usage dashboard data retrieved successfully'
  };

  res.json(response);
}));

// DELETE /api/token-usage/logs - Clear all logs (admin only)
router.delete('/logs', asyncHandler(async (req: Request, res: Response) => {
  // Note: In a real application, you'd want to add admin authentication here
  TokenUsageLogger.clearLogs();

  const response: ApiResponse<null> = {
    success: true,
    data: null,
    message: 'All token usage logs cleared successfully'
  };

  res.json(response);
}));

export default router;
