# Load environment variables from .env.docker file
# Copy .env.docker.example to .env.docker and update values
services:
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: rpa-nginx
    ports:
      - "80:80"
      - "3000:80"
      - "443:443"
    volumes:
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - rpa-network

  redis:
    image: redis:7-alpine
    container_name: rpa-redis
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy noeviction
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - rpa-network

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: rpa-backend
    env_file:
      - .env.docker
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - PORT=44300
      - USE_HTTPS=false
      - DOCKER_ENV=true
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./screenshots:/app/screenshots
      - ./downloads:/app/downloads
      - ./backend/data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:44300/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - rpa-network

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: rpa-frontend
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    networks:
      - rpa-network

volumes:
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  rpa-network:
    driver: bridge
