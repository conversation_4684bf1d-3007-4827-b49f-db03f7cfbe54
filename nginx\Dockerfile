FROM nginx:alpine

# Install htpasswd for basic auth
RUN apk add --no-cache apache2-utils

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/conf.d/default.conf

# Create directory for auth files
RUN mkdir -p /etc/nginx/auth

# Create default htpasswd file (username: admin, password: rpa123)
# In production, override this with your own credentials
RUN htpasswd -cb /etc/nginx/auth/.htpasswd admin rpa123

# Create SSL directory for future SSL certificates
RUN mkdir -p /etc/nginx/ssl

EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]
