services:
  redis:
    image: redis:7-alpine
    container_name: rpa-redis-dev
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped

  shared-dev:
    image: node:20
    container_name: rpa-shared-dev
    working_dir: /app/shared
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    command: ["sh", "/app/scripts/start-shared-dev.sh"]
    volumes:
      - ./shared:/app/shared
      - ./scripts:/app/scripts:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - shared_node_modules:/app/shared/node_modules
    restart: unless-stopped

  backend-dev:
    image: mcr.microsoft.com/playwright:v1.55.0-jammy
    container_name: rpa-backend-dev
    working_dir: /app/backend
    environment:
      - NODE_ENV=development
      - PORT=3002
      - REDIS_URL=redis://redis:6379
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CHOKIDAR_USEPOLLING=true
    command: ["sh", "/app/scripts/start-backend-dev.sh"]
    ports:
      - "3002:3002"
    volumes:
      - ./backend:/app/backend
      - ./shared:/app/shared
      - ./scripts:/app/scripts:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - backend_node_modules:/app/backend/node_modules
      - shared_node_modules:/app/shared/node_modules
    depends_on:
      redis:
        condition: service_healthy
      shared-dev:
        condition: service_started
    restart: unless-stopped

  frontend-dev:
    image: node:20
    container_name: rpa-frontend-dev
    working_dir: /app/frontend
    environment:
      - NODE_ENV=development
      - BACKEND_URL=http://backend-dev:3002
      - VITE_API_BASE=http://localhost:3002
      - CHOKIDAR_USEPOLLING=true
      - VITE_LOG_LEVEL=info
    command: ["sh", "/app/scripts/start-frontend-dev.sh"]
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app/frontend
      - ./shared:/app/shared
      - ./scripts:/app/scripts:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - frontend_node_modules:/app/frontend/node_modules
      - shared_node_modules:/app/shared/node_modules
    depends_on:
      redis:
        condition: service_healthy
      backend-dev:
        condition: service_started
      shared-dev:
        condition: service_started
    restart: unless-stopped

volumes:
  backend_node_modules:
  frontend_node_modules:
  shared_node_modules:

